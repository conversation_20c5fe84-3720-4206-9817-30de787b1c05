{"color": {"brand": {"900": {"value": "#064420", "description": "Brand color - darkest green", "accessibility": {"contrast-white": "15.8:1", "contrast-black": "1.3:1", "wcag-aa": true}}, "700": {"value": "#0b8457", "description": "Brand color - dark green", "accessibility": {"contrast-white": "9.2:1", "contrast-black": "2.3:1", "wcag-aa": true}}, "600": {"value": "#56ab2f", "description": "Brand color - medium green", "accessibility": {"contrast-white": "4.7:1", "contrast-black": "4.4:1", "wcag-aa": true}}, "400": {"value": "#16c79a", "description": "Brand color - light green", "accessibility": {"contrast-white": "2.8:1", "contrast-black": "7.4:1", "wcag-aa": true}}, "light": {"value": "#a8e6cf", "description": "Background light green", "accessibility": {"contrast-white": "1.4:1", "contrast-black": "14.8:1", "wcag-aa": false, "note": "Usare solo per sfondi chiari con testo scuro"}}}, "semantic": {"primary": {"value": "{color.brand.600}", "description": "Colore primario per CTA e azioni principali"}, "secondary": {"value": "{color.brand.400}", "description": "Colore secondario per elementi di supporto"}, "success": {"value": "#10b981", "description": "Verde per stati positivi (diverso dal brand)"}, "warning": {"value": "#f59e0b", "description": "Giallo per avvisi e stati di attenzione"}, "error": {"value": "#ef4444", "description": "Rosso per errori e stati negativi"}, "info": {"value": "{color.brand.400}", "description": "Colore informativo"}}, "text": {"primary": {"value": "#1a1a1a", "description": "Testo principale su sfondo chiaro", "accessibility": {"contrast-bg-light": "13.2:1", "wcag-aa": true}}, "secondary": {"value": "#4a4a4a", "description": "<PERSON>o second<PERSON>", "accessibility": {"contrast-bg-light": "8.9:1", "wcag-aa": true}}, "muted": {"value": "#6b7280", "description": "Testo attenuato", "accessibility": {"contrast-bg-light": "5.6:1", "wcag-aa": true}}, "on-dark": {"value": "#ffffff", "description": "Testo bianco su sfondo scuro", "accessibility": {"contrast-brand-900": "15.8:1", "wcag-aa": true}}, "on-primary": {"value": "#ffffff", "description": "Testo bianco su brand colors", "accessibility": {"contrast-brand-600": "4.7:1", "wcag-aa": true}}}, "background": {"primary": {"value": "#ffffff", "description": "Sfondo principale"}, "light": {"value": "{color.brand.light}", "description": "Sfondo verde chiaro"}, "surface": {"value": "#ffffff", "description": "Sfondo per card e superfici"}, "surface-light": {"value": "#f8f9fa", "description": "Sfondo leggero per sezioni"}}, "border": {"default": {"value": "#e5e7eb", "description": "Bordo standard"}, "light": {"value": "#f3f4f6", "description": "<PERSON><PERSON> chiaro"}, "focus": {"value": "{color.brand.400}", "description": "<PERSON><PERSON> per elementi focus"}}, "shadow": {"sm": {"value": "0 1px 2px 0 rgba(6, 68, 32, 0.05)", "description": "Ombra piccola"}, "md": {"value": "0 4px 6px -1px rgba(6, 68, 32, 0.1), 0 2px 4px -1px rgba(6, 68, 32, 0.06)", "description": "Ombra media"}, "lg": {"value": "0 10px 15px -3px rgba(6, 68, 32, 0.1), 0 4px 6px -2px rgba(6, 68, 32, 0.05)", "description": "Ombra grande"}, "xl": {"value": "0 20px 25px -5px rgba(6, 68, 32, 0.1), 0 10px 10px -5px rgba(6, 68, 32, 0.04)", "description": "Ombra extra large"}}}, "spacing": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem"}, "typography": {"font-family": {"primary": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "mono": "Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace"}, "font-size": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem"}, "font-weight": {"normal": "400", "medium": "500", "semibold": "600", "bold": "700"}, "line-height": {"tight": "1.25", "normal": "1.5", "relaxed": "1.625"}}, "border-radius": {"sm": "0.25rem", "md": "0.375rem", "lg": "0.5rem", "xl": "0.75rem", "2xl": "1rem", "full": "9999px"}, "transition": {"duration": {"fast": "150ms", "normal": "200ms", "slow": "300ms"}, "easing": {"ease": "ease", "ease-in": "ease-in", "ease-out": "ease-out", "ease-in-out": "ease-in-out"}}}