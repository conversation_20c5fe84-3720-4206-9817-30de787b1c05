const Joi = require('joi');

/**
 * Middleware factory for validating request query parameters
 * @param {Joi.Schema} schema - Joi validation schema
 */
function validateQuery(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query);
    
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      });
    }
    
    // Replace req.query with validated and transformed values
    req.query = value;
    next();
  };
}

/**
 * Middleware factory for validating request body
 * @param {Joi.Schema} schema - Joi validation schema
 */
function validateBody(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      });
    }
    
    // Replace req.body with validated and transformed values
    req.body = value;
    next();
  };
}

/**
 * Middleware factory for validating request parameters
 * @param {Joi.Schema} schema - Joi validation schema
 */
function validateParams(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.params);
    
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      });
    }
    
    // Replace req.params with validated and transformed values
    req.params = value;
    next();
  };
}

/**
 * Middleware for validating user input (create/update)
 */
function validateUserInput(req, res, next) {
  const schema = Joi.object({
    username: Joi.string().min(3).max(50).required(),
    password: Joi.string().min(8).required(),
    account_type: Joi.string().valid('normal', 'admin').default('normal')
  });
  
  const { error, value } = schema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      success: false,
      message: error.details[0].message,
      field: error.details[0].path.join('.')
    });
  }
  
  // Replace req.body with validated and transformed values
  req.body = value;
  next();
}

/**
 * Middleware for validating password input
 */
function validatePassword(req, res, next) {
  const schema = Joi.object({
    password: Joi.string().min(8).required()
  });

  const { error, value } = schema.validate(req.body);

  if (error) {
    return res.status(400).json({
      success: false,
      message: error.details[0].message,
      field: error.details[0].path.join('.')
    });
  }

  // Replace req.body with validated and transformed values
  req.body = value;
  next();
}

/**
 * Middleware for validating vehicle input (create/update)
 */
function validateVehicleInput(req, res, next) {
  const schema = Joi.object({
    imei: Joi.string().pattern(/^\d{7,15}$/).required().messages({
      'string.pattern.base': 'IMEI must be numeric and between 7-15 digits'
    }),
    sim_number: Joi.string().min(10).max(20).required(),
    name: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(255).optional().allow('')
  });

  const { error, value } = schema.validate(req.body);

  if (error) {
    return res.status(400).json({
      success: false,
      message: error.details[0].message,
      field: error.details[0].path.join('.')
    });
  }

  // Replace req.body with validated and transformed values
  req.body = value;
  next();
}

module.exports = {
  validateQuery,
  validateBody,
  validateParams,
  validateUserInput,
  validatePassword,
  validateVehicleInput
};
