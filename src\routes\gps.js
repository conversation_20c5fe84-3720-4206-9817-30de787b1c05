const express = require('express');
const router = express.Router();
const {
  getGpsData,
  getLatestPosition,
  getAllVehiclesWithPositions
} = require('../services/gpsService');
const { authenticateToken } = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const gpsQuerySchema = Joi.object({
  imei: Joi.string().length(15).required(),
  limit: Joi.number().integer().min(1).max(1000).default(100),
  offset: Joi.number().integer().min(0).default(0),
  startDate: Joi.date().iso(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')),
  orderBy: Joi.string().valid('device_timestamp', 'server_timestamp', 'speed').default('device_timestamp'),
  order: Joi.string().valid('ASC', 'DESC').default('DESC')
});

const latestPositionSchema = Joi.object({
  imei: Joi.string().length(15).required()
});

/**
 * GET /api/gps/data
 * Get GPS data for a specific IMEI with pagination and filtering
 */
router.get('/data',
  authenticateToken,
  async (req, res) => {
    try {
      // Validate query parameters
      const { error, value } = gpsQuerySchema.validate(req.query);
      if (error) {
        return res.status(400).json({
          success: false,
          message: error.details[0].message
        });
      }

      const { imei, limit, offset, startDate, endDate, orderBy, order } = value;

      // Check vehicle access
      const hasAccess = await req.user.hasVehicleAccess(imei);
      if (!hasAccess && req.user.account_type !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied to this vehicle'
        });
      }

      const options = {
        limit: parseInt(limit),
        offset: parseInt(offset),
        orderBy,
        order
      };

      if (startDate) options.startDate = new Date(startDate);
      if (endDate) options.endDate = new Date(endDate);

      const result = await getGpsData(imei, options);

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });

    } catch (error) {
      console.error('Error getting GPS data:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/gps/latest/:imei
 * Get latest GPS position for a specific IMEI
 */
router.get('/latest/:imei',
  authenticateToken,
  async (req, res) => {
    try {
      const { imei } = req.params;
      
      // Validate IMEI format
      if (!imei || imei.length !== 15) {
        return res.status(400).json({
          success: false,
          message: 'Invalid IMEI format'
        });
      }
      
      // Check if user has access to this vehicle
      const hasAccess = await req.user.hasVehicleAccess(imei);
      if (!hasAccess && req.user.account_type !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied to this vehicle'
        });
      }
      
      const position = await getLatestPosition(imei);
      
      if (!position) {
        return res.status(404).json({
          success: false,
          message: 'No GPS data found for this IMEI'
        });
      }
      
      res.json({
        success: true,
        data: position
      });
      
    } catch (error) {
      console.error('Error getting latest position:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/gps/vehicles
 * Get all vehicles with their latest positions (admin only or user's vehicles)
 */
router.get('/vehicles',
  authenticateToken,
  async (req, res) => {
    try {
      let vehicles = await getAllVehiclesWithPositions();
      
      // Filter vehicles based on user permissions
      if (req.user.account_type !== 'admin') {
        const userVehicleImeis = await req.user.getVehicleImeis();
        vehicles = vehicles.filter(vehicle => 
          userVehicleImeis.includes(vehicle.imei)
        );
      }
      
      res.json({
        success: true,
        data: vehicles
      });
      
    } catch (error) {
      console.error('Error getting vehicles:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/gps/track/:imei
 * Get GPS track (route) for a specific IMEI within a time range
 */
router.get('/track/:imei',
  authenticateToken,
  async (req, res) => {
    try {
      const { imei } = req.params;
      const { startDate, endDate, minInterval = 60 } = req.query;
      
      // Validate IMEI
      if (!imei || imei.length !== 15) {
        return res.status(400).json({
          success: false,
          message: 'Invalid IMEI format'
        });
      }
      
      // Check access
      const hasAccess = await req.user.hasVehicleAccess(imei);
      if (!hasAccess && req.user.account_type !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied to this vehicle'
        });
      }
      
      // Default to last 24 hours if no dates provided
      const end = endDate ? new Date(endDate) : new Date();
      const start = startDate ? new Date(startDate) : new Date(end.getTime() - 24 * 60 * 60 * 1000);
      
      const options = {
        startDate: start,
        endDate: end,
        limit: 10000, // Large limit for track data
        orderBy: 'device_timestamp',
        order: 'ASC'
      };
      
      const result = await getGpsData(imei, options);
      
      // Filter points to reduce data size (keep points with minimum time interval)
      const filteredData = [];
      let lastTimestamp = null;
      
      for (const point of result.data) {
        if (!lastTimestamp || 
            (new Date(point.device_timestamp) - lastTimestamp) >= (minInterval * 1000)) {
          filteredData.push({
            latitude: parseFloat(point.latitude),
            longitude: parseFloat(point.longitude),
            timestamp: point.device_timestamp,
            speed: point.speed,
            direction: point.direction,
            ignition_status: point.ignition_status
          });
          lastTimestamp = new Date(point.device_timestamp);
        }
      }
      
      res.json({
        success: true,
        data: filteredData,
        summary: {
          totalPoints: result.data.length,
          filteredPoints: filteredData.length,
          timeRange: { start, end }
        }
      });
      
    } catch (error) {
      console.error('Error getting GPS track:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;
