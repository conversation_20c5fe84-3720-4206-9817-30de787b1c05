-- Create database (run this manually in PostgreSQL)
-- CREATE DATABASE teltonika_gps;

-- Connect to the database and run the following:

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    account_type VARCHAR(20) DEFAULT 'normal' CHECK (account_type IN ('normal', 'admin')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle descriptions table
CREATE TABLE IF NOT EXISTS vehicles (
    id SERIAL PRIMARY KEY,
    imei VARCHAR(15) UNIQUE NOT NULL CHECK (imei ~ '^[0-9]{7,15}$'),
    sim_number VARCHAR(20) UNIQUE NOT NULL,
    description VARCHAR(255),
    name <PERSON><PERSON>HA<PERSON>(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User-Vehicle associations (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_vehicles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    vehicle_id INTEGER REFERENCES vehicles(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, vehicle_id)
);

-- GPS data table
CREATE TABLE IF NOT EXISTS gps_data (
    id SERIAL PRIMARY KEY,
    imei VARCHAR(15) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    device_timestamp TIMESTAMP NOT NULL,
    server_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    speed INTEGER DEFAULT 0,
    direction INTEGER DEFAULT 0,
    altitude INTEGER DEFAULT 0,
    ignition_status BOOLEAN DEFAULT FALSE,
    battery_voltage DECIMAL(5, 1) DEFAULT 0.0,
    total_odometer DECIMAL(10, 1) DEFAULT 0.0,
    external_voltage DECIMAL(5, 1) DEFAULT 0.0,
    variable_data JSONB,
    satellites INTEGER DEFAULT 0
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_gps_data_imei ON gps_data(imei);
CREATE INDEX IF NOT EXISTS idx_gps_data_device_timestamp ON gps_data(device_timestamp);
CREATE INDEX IF NOT EXISTS idx_gps_data_server_timestamp ON gps_data(server_timestamp);
CREATE INDEX IF NOT EXISTS idx_gps_data_imei_timestamp ON gps_data(imei, device_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_vehicles_imei ON vehicles(imei);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_user_vehicles_user_id ON user_vehicles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_vehicles_vehicle_id ON user_vehicles(vehicle_id);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_vehicles_updated_at ON vehicles;
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON vehicles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
