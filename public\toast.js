/**
 * Toast notification component
 * Provides toast notifications and confirmation dialogs
 */

// Toast functions
function showToast(type, message, duration = 5000) {
    // Ensure toast container exists
    ensureToastContainer();

    const toastContainer = document.getElementById('toastContainer');

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;

    // Define icons for different types
    const icons = {
        success: '✓',
        error: '✕',
        info: 'ℹ',
        warning: '⚠'
    };

    toast.innerHTML = `
        <span class="toast-icon">${icons[type] || 'ℹ'}</span>
        <div class="toast-message">${message}</div>
        <button class="toast-close">×</button>
    `;

    // Add to container
    toastContainer.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // Auto remove
    const removeToast = () => {
        toast.classList.remove('show');
        toast.classList.add('hide');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    };

    // Auto remove after duration
    const autoRemoveTimeout = setTimeout(removeToast, duration);

    // Manual close
    toast.querySelector('.toast-close').addEventListener('click', () => {
        clearTimeout(autoRemoveTimeout);
        removeToast();
    });
}

// Helper functions for different toast types
function showToastSuccess(message, duration) {
    showToast('success', message, duration);
}

function showToastError(message, duration) {
    showToast('error', message, duration);
}

function showToastInfo(message, duration) {
    showToast('info', message, duration);
}

function showToastWarning(message, duration) {
    showToast('warning', message, duration);
}

// Confirm toast function (returns Promise<boolean>)
function showConfirmToast(message) {
    // Ensure toast container exists
    ensureToastContainer();

    return new Promise((resolve) => {
        const toastContainer = document.getElementById('toastContainer');

        // Create confirm toast element
        const toast = document.createElement('div');
        toast.className = 'toast toast-confirm';

        toast.innerHTML = `
            <span class="toast-icon">⚠</span>
            <div class="toast-message">${message}</div>
            <div class="toast-actions">
                <button class="btn-cancel">Annulla</button>
                <button class="btn-confirm">Elimina</button>
            </div>
        `;

        // Add to container
        toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Remove toast function
        const removeToast = () => {
            toast.classList.remove('show');
            toast.classList.add('hide');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        };

        // Confirm button
        const confirmBtn = toast.querySelector('.btn-confirm');
        confirmBtn.addEventListener('click', () => {
            removeToast();
            resolve(true);
        });

        // Cancel button
        const cancelBtn = toast.querySelector('.btn-cancel');
        cancelBtn.addEventListener('click', () => {
            removeToast();
            resolve(false);
        });

        // Hide close button for confirm dialogs
        const closeBtn = toast.querySelector('.toast-close');
        if (closeBtn) {
            closeBtn.style.display = 'none';
        }
    });
}

// Initialize toast container if it doesn't exist
function ensureToastContainer() {
    let container = document.getElementById('toastContainer');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container';
        container.id = 'toastContainer';
        document.body.appendChild(container);
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    ensureToastContainer();
});

// Exports for modular use (if using modules)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showToast,
        showToastSuccess,
        showToastError,
        showToastInfo,
        showToastWarning,
        showConfirmToast,
        ensureToastContainer
    };
}
