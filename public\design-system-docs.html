<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECOTrac Design System Documentation</title>
    <link rel="stylesheet" href="design-system.css">
    <style>
        .docs-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-xl);
        }
        
        .docs-section {
            margin-bottom: var(--space-3xl);
        }
        
        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-lg);
            margin: var(--space-lg) 0;
        }
        
        .color-swatch {
            width: 100%;
            height: 60px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--space-sm);
        }
        
        .component-demo {
            padding: var(--space-lg);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-lg);
            margin: var(--space-md) 0;
        }
        
        .code-block {
            background: var(--color-surface-light);
            padding: var(--space-md);
            border-radius: var(--radius-md);
            font-family: var(--font-family-mono);
            font-size: var(--font-size-sm);
            overflow-x: auto;
            margin: var(--space-md) 0;
        }
        
        .spacing-demo {
            background: var(--brand-400);
            color: white;
            text-align: center;
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
        }
        
        .typography-demo {
            margin: var(--space-sm) 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="header-title">
            <img src="images/logo-ecotrac-bianco.png" alt="ECOTrac Logo" class="logo">
        </h1>
        <div class="header-nav">
            <a href="index.html" class="btn">Back to App</a>
        </div>
    </div>

    <div class="docs-container">
        <div class="docs-section">
            <h1>ECOTrac Design System</h1>
            <p>A comprehensive design system for consistent, accessible, and maintainable UI development.</p>
        </div>

        <!-- Getting Started -->
        <div class="docs-section">
            <h2>Getting Started</h2>
            <p>To use the ECOTrac Design System, simply include the master CSS file in your HTML:</p>
            <div class="code-block">
&lt;link rel="stylesheet" href="design-system.css"&gt;
            </div>
            <p>This single import includes all design system components, utilities, and variables.</p>
        </div>

        <!-- Color System -->
        <div class="docs-section">
            <h2>Color System</h2>
            <p>Our color palette is built around ECOTrac's green brand identity with semantic color assignments.</p>
            
            <h3>Brand Colors</h3>
            <div class="docs-grid">
                <div class="card">
                    <div class="color-swatch bg-brand-900">Brand 900</div>
                    <code>var(--brand-900)</code><br>
                    <small>#064420</small>
                </div>
                <div class="card">
                    <div class="color-swatch bg-brand-700">Brand 700</div>
                    <code>var(--brand-700)</code><br>
                    <small>#0b8457</small>
                </div>
                <div class="card">
                    <div class="color-swatch bg-brand-600">Brand 600</div>
                    <code>var(--brand-600)</code><br>
                    <small>#56ab2f</small>
                </div>
                <div class="card">
                    <div class="color-swatch bg-brand-400">Brand 400</div>
                    <code>var(--brand-400)</code><br>
                    <small>#16c79a</small>
                </div>
            </div>

            <h3>Semantic Colors</h3>
            <div class="docs-grid">
                <div class="card">
                    <div class="color-swatch bg-primary">Primary</div>
                    <code>var(--color-primary)</code>
                </div>
                <div class="card">
                    <div class="color-swatch bg-success">Success</div>
                    <code>var(--color-success)</code>
                </div>
                <div class="card">
                    <div class="color-swatch bg-warning">Warning</div>
                    <code>var(--color-warning)</code>
                </div>
                <div class="card">
                    <div class="color-swatch bg-error">Error</div>
                    <code>var(--color-error)</code>
                </div>
            </div>
        </div>

        <!-- Typography -->
        <div class="docs-section">
            <h2>Typography</h2>
            <p>Consistent typography scale using system fonts for optimal performance and readability.</p>
            
            <div class="component-demo">
                <div class="typography-demo text-4xl">Heading 1 - 4xl</div>
                <div class="typography-demo text-3xl">Heading 2 - 3xl</div>
                <div class="typography-demo text-2xl">Heading 3 - 2xl</div>
                <div class="typography-demo text-xl">Heading 4 - xl</div>
                <div class="typography-demo text-lg">Heading 5 - lg</div>
                <div class="typography-demo text-base">Body Text - base</div>
                <div class="typography-demo text-sm">Small Text - sm</div>
                <div class="typography-demo text-xs">Extra Small - xs</div>
            </div>

            <div class="code-block">
&lt;h1 class="text-4xl font-bold"&gt;Large Heading&lt;/h1&gt;
&lt;p class="text-base leading-normal"&gt;Body text&lt;/p&gt;
&lt;small class="text-sm text-muted"&gt;Small text&lt;/small&gt;
            </div>
        </div>

        <!-- Spacing System -->
        <div class="docs-section">
            <h2>Spacing System</h2>
            <p>Consistent spacing scale based on 4px increments for harmonious layouts.</p>
            
            <div class="docs-grid">
                <div class="card">
                    <div class="spacing-demo p-xs">XS - 4px</div>
                    <code>var(--space-xs)</code>
                </div>
                <div class="card">
                    <div class="spacing-demo p-sm">SM - 8px</div>
                    <code>var(--space-sm)</code>
                </div>
                <div class="card">
                    <div class="spacing-demo p-md">MD - 16px</div>
                    <code>var(--space-md)</code>
                </div>
                <div class="card">
                    <div class="spacing-demo p-lg">LG - 24px</div>
                    <code>var(--space-lg)</code>
                </div>
                <div class="card">
                    <div class="spacing-demo p-xl">XL - 32px</div>
                    <code>var(--space-xl)</code>
                </div>
                <div class="card">
                    <div class="spacing-demo p-2xl">2XL - 48px</div>
                    <code>var(--space-2xl)</code>
                </div>
            </div>

            <div class="code-block">
&lt;div class="p-md m-lg"&gt;Padding medium, margin large&lt;/div&gt;
&lt;div class="pt-xl pb-sm"&gt;Padding top XL, bottom small&lt;/div&gt;
            </div>
        </div>

        <!-- Button Components -->
        <div class="docs-section">
            <h2>Button Components</h2>
            <p>Consistent button styling with multiple variants and states.</p>
            
            <div class="component-demo">
                <div class="flex gap-md flex-wrap">
                    <button class="btn">Primary</button>
                    <button class="btn btn-secondary">Secondary</button>
                    <button class="btn btn-success">Success</button>
                    <button class="btn btn-warning">Warning</button>
                    <button class="btn btn-danger">Danger</button>
                    <button class="btn btn-ghost">Ghost</button>
                </div>
                
                <div class="flex gap-md flex-wrap mt-md">
                    <button class="btn btn-sm">Small</button>
                    <button class="btn">Default</button>
                    <button class="btn btn-lg">Large</button>
                </div>
                
                <div class="mt-md">
                    <button class="btn btn-full">Full Width</button>
                </div>
            </div>

            <div class="code-block">
&lt;button class="btn"&gt;Primary Button&lt;/button&gt;
&lt;button class="btn btn-secondary"&gt;Secondary&lt;/button&gt;
&lt;button class="btn btn-sm"&gt;Small Button&lt;/button&gt;
&lt;button class="btn btn-full"&gt;Full Width&lt;/button&gt;
            </div>
        </div>

        <!-- Form Components -->
        <div class="docs-section">
            <h2>Form Components</h2>
            <p>Accessible form controls with consistent styling and validation states.</p>
            
            <div class="component-demo">
                <div class="form-group">
                    <label class="form-label">Text Input</label>
                    <input type="text" class="form-input" placeholder="Enter text...">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Select Dropdown</label>
                    <select class="form-select">
                        <option>Choose option...</option>
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Textarea</label>
                    <textarea class="form-textarea" placeholder="Enter message..."></textarea>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" id="check1">
                    <label for="check1">Checkbox option</label>
                </div>
            </div>

            <div class="code-block">
&lt;div class="form-group"&gt;
    &lt;label class="form-label"&gt;Label&lt;/label&gt;
    &lt;input type="text" class="form-input" placeholder="Placeholder"&gt;
&lt;/div&gt;
            </div>
        </div>

        <!-- Card Components -->
        <div class="docs-section">
            <h2>Card Components</h2>
            <p>Flexible card containers for organizing content.</p>
            
            <div class="component-demo">
                <div class="docs-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Card Title</h3>
                            <p class="card-subtitle">Card subtitle</p>
                        </div>
                        <div class="card-body">
                            <p>Card content goes here. This is a standard card with header and body.</p>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-sm">Action</button>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h4 class="info-card-title">Info Card</h4>
                        <div class="info-card-value">42</div>
                    </div>
                </div>
            </div>

            <div class="code-block">
&lt;div class="card"&gt;
    &lt;div class="card-header"&gt;
        &lt;h3 class="card-title"&gt;Title&lt;/h3&gt;
    &lt;/div&gt;
    &lt;div class="card-body"&gt;
        Content
    &lt;/div&gt;
&lt;/div&gt;
            </div>
        </div>

        <!-- Utility Classes -->
        <div class="docs-section">
            <h2>Utility Classes</h2>
            <p>Utility classes for rapid development and consistent styling.</p>
            
            <h3>Layout Utilities</h3>
            <div class="code-block">
.flex, .grid, .block, .inline-block, .hidden
.justify-center, .justify-between, .items-center
.w-full, .h-full, .max-w-lg
            </div>
            
            <h3>Spacing Utilities</h3>
            <div class="code-block">
.m-{size}, .p-{size}  /* margin, padding */
.mt-{size}, .pt-{size}  /* top */
.mb-{size}, .pb-{size}  /* bottom */
.ml-{size}, .pl-{size}  /* left */
.mr-{size}, .pr-{size}  /* right */
.gap-{size}  /* flexbox/grid gap */
            </div>
            
            <h3>Typography Utilities</h3>
            <div class="code-block">
.text-{size}  /* xs, sm, base, lg, xl, 2xl, 3xl, 4xl */
.font-{weight}  /* normal, medium, semibold, bold */
.text-{color}  /* primary, secondary, muted, success, error */
.text-center, .text-left, .text-right
            </div>
        </div>

        <!-- Best Practices -->
        <div class="docs-section">
            <h2>Best Practices</h2>
            
            <div class="card">
                <div class="card-body">
                    <h3>Do's</h3>
                    <ul>
                        <li>Use design system variables instead of hardcoded values</li>
                        <li>Prefer utility classes over custom CSS when possible</li>
                        <li>Use semantic color names (primary, success, error) over brand colors</li>
                        <li>Follow the spacing scale for consistent layouts</li>
                        <li>Test components in different states (hover, focus, disabled)</li>
                    </ul>
                    
                    <h3>Don'ts</h3>
                    <ul>
                        <li>Don't use inline styles - use utility classes instead</li>
                        <li>Don't create custom components without checking existing ones first</li>
                        <li>Don't hardcode colors, spacing, or typography values</li>
                        <li>Don't ignore accessibility requirements (focus states, contrast)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- File Structure -->
        <div class="docs-section">
            <h2>File Structure</h2>
            <div class="code-block">
design-system.css      # Master file (import this)
├── color-system.css   # Variables and color utilities
├── components.css     # Core UI components
├── utilities.css      # Utility classes
├── forms.css         # Enhanced form components
└── toast.css         # Toast notifications
            </div>
        </div>
    </div>
</body>
</html>
