const { Client } = require('pg');
require('dotenv').config();

async function setupDatabase() {
  console.log('🔧 Setting up PostgreSQL database...\n');
  
  // First, connect to PostgreSQL without specifying a database to create our database
  const adminClient = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
    database: 'postgres' // Connect to default postgres database
  });

  try {
    console.log('📡 Connecting to PostgreSQL server...');
    await adminClient.connect();
    console.log('✅ Connected to PostgreSQL server successfully!');

    // Check if database exists
    const dbName = process.env.DB_NAME || 'Tracker_database';
    console.log(`🔍 Checking if database '${dbName}' exists...`);
    
    const dbCheckResult = await adminClient.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [dbName]
    );

    if (dbCheckResult.rows.length === 0) {
      console.log(`📦 Creating database '${dbName}'...`);
      await adminClient.query(`CREATE DATABASE "${dbName}"`);
      console.log(`✅ Database '${dbName}' created successfully!`);
    } else {
      console.log(`✅ Database '${dbName}' already exists.`);
    }

  } catch (error) {
    console.error('❌ Error setting up database:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n🚨 PostgreSQL Connection Failed!');
      console.log('Please ensure PostgreSQL is installed and running:');
      console.log('');
      console.log('Windows:');
      console.log('  1. Download from: https://www.postgresql.org/download/windows/');
      console.log('  2. Install and start the PostgreSQL service');
      console.log('  3. Make sure the password matches your .env file');
      console.log('');
      console.log('Or use Docker:');
      console.log('  docker-compose up postgres -d');
      console.log('');
    } else if (error.code === '28P01') {
      console.log('\n🚨 Authentication Failed!');
      console.log('Please check your database credentials in the .env file:');
      console.log(`  DB_USER=${process.env.DB_USER}`);
      console.log(`  DB_PASSWORD=${process.env.DB_PASSWORD ? '[SET]' : '[NOT SET]'}`);
    }
    
    process.exit(1);
  } finally {
    await adminClient.end();
  }

  // Now test connection to our specific database
  console.log(`\n🔗 Testing connection to '${process.env.DB_NAME}'...`);
  const appClient = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'Tracker_database'
  });

  try {
    await appClient.connect();
    console.log('✅ Application database connection successful!');
    
    // Test a simple query
    const result = await appClient.query('SELECT NOW() as current_time');
    console.log(`⏰ Database time: ${result.rows[0].current_time}`);
    
  } catch (error) {
    console.error('❌ Error connecting to application database:', error.message);
    process.exit(1);
  } finally {
    await appClient.end();
  }

  console.log('\n🎉 Database setup completed successfully!');
  console.log('You can now run: npm run db:migrate');
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;
