const pool = require('../config/database');
const logger = require('../utils/logger');

/**
 * Create a new vehicle
 * @param {Object} vehicleData - Vehicle data
 */
async function createVehicle(vehicleData) {
  const client = await pool.connect();

  try {
    const { imei, sim_number, name, description = '' } = vehicleData;

    // Check if vehicle already exists
    const existingVehicle = await client.query(
      'SELECT id FROM vehicles WHERE imei = $1',
      [imei]
    );

    if (existingVehicle.rows.length > 0) {
      throw new Error('Vehicle with this IMEI already exists');
    }

    // Create vehicle
    const query = `
      INSERT INTO vehicles (imei, sim_number, name, description)
      VALUES ($1, $2, $3, $4)
      RETURNING id, imei, sim_number, name, description, created_at;
    `;

    const result = await client.query(query, [imei, sim_number, name, description]);

    logger.info(`Vehicle created: ${imei}`);
    return result.rows[0];

  } catch (error) {
    logger.error('Error creating vehicle:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get vehicle by ID
 * @param {number} vehicleId
 */
async function getVehicleById(vehicleId) {
  const client = await pool.connect();

  try {
    const query = `
      SELECT id, imei, sim_number, name, description, created_at, updated_at
      FROM vehicles
      WHERE id = $1;
    `;

    const result = await client.query(query, [vehicleId]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];

  } catch (error) {
    logger.error('Error getting vehicle by ID:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get vehicle by IMEI
 * @param {string} imei
 */
async function getVehicleByImei(imei) {
  const client = await pool.connect();

  try {
    const query = `
      SELECT id, imei, sim_number, name, description, created_at, updated_at
      FROM vehicles
      WHERE imei = $1;
    `;

    const result = await client.query(query, [imei]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];

  } catch (error) {
    logger.error('Error getting vehicle by IMEI:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get vehicle by SIM number
 * @param {string} sim_number
 */
async function getVehicleBySimNumber(sim_number) {
  const client = await pool.connect();

  try {
    const query = `
      SELECT id, imei, sim_number, name, description, created_at, updated_at
      FROM vehicles
      WHERE sim_number = $1;
    `;

    const result = await client.query(query, [sim_number]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0];

  } catch (error) {
    logger.error('Error getting vehicle by SIM number:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get all vehicles
 */
async function getAllVehicles() {
  const client = await pool.connect();

  try {
    const query = `
      SELECT id, imei, sim_number, name, description, created_at, updated_at
      FROM vehicles
      ORDER BY name;
    `;

    const result = await client.query(query);
    return result.rows;

  } catch (error) {
    logger.error('Error getting all vehicles:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Update vehicle information
 * @param {number} vehicleId
 * @param {Object} vehicleData
 */
async function updateVehicle(vehicleId, vehicleData) {
  const client = await pool.connect();

  try {
    const { imei, sim_number, name, description } = vehicleData;

    // Prepare update query
    const fields = [];
    const values = [];
    let index = 1;

    if (imei !== undefined) {
      fields.push(`imei = $${index}`);
      values.push(imei);
      index++;
    }

    if (sim_number !== undefined) {
      fields.push(`sim_number = $${index}`);
      values.push(sim_number);
      index++;
    }

    if (name !== undefined) {
      fields.push(`name = $${index}`);
      values.push(name);
      index++;
    }

    if (description !== undefined) {
      fields.push(`description = $${index}`);
      values.push(description);
      index++;
    }

    // If no fields to update, return the vehicle
    if (fields.length === 0) {
      const query = 'SELECT id, imei, sim_number, name, description, created_at, updated_at FROM vehicles WHERE id = $1';
      const result = await client.query(query, [vehicleId]);
      return result.rows[0];
    }

    // Add updated_at timestamp
    fields.push(`updated_at = CURRENT_TIMESTAMP`);

    values.push(vehicleId);

    const query = `
      UPDATE vehicles
      SET ${fields.join(', ')}
      WHERE id = $${index}
      RETURNING id, imei, sim_number, name, description, created_at, updated_at;
    `;

    const result = await client.query(query, values);
    return result.rows[0];

  } catch (error) {
    logger.error('Error updating vehicle:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Associate vehicle with user
 * @param {number} userId
 * @param {number} vehicleId
 */
async function associateVehicleWithUser(userId, vehicleId) {
  const client = await pool.connect();

  try {
    const query = `
      INSERT INTO user_vehicles (user_id, vehicle_id)
      VALUES ($1, $2)
      ON CONFLICT (user_id, vehicle_id) DO NOTHING;
    `;

    await client.query(query, [userId, vehicleId]);
    logger.info(`Vehicle ${vehicleId} associated with user ${userId}`);

  } catch (error) {
    logger.error('Error associating vehicle with user:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Remove vehicle-user association
 * @param {number} userId
 * @param {number} vehicleId
 */
async function removeVehicleUserAssociation(userId, vehicleId) {
  const client = await pool.connect();

  try {
    const query = 'DELETE FROM user_vehicles WHERE user_id = $1 AND vehicle_id = $2';
    await client.query(query, [userId, vehicleId]);
    logger.info(`Removed association between user ${userId} and vehicle ${vehicleId}`);

  } catch (error) {
    logger.error('Error removing vehicle-user association:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get all vehicles associated with a specific user
 * @param {number} userId
 */
async function getUserVehicles(userId) {
  const client = await pool.connect();

  try {
    const query = `
      SELECT v.id, v.imei, v.sim_number, v.name, v.description, v.created_at, v.updated_at
      FROM vehicles v
      INNER JOIN user_vehicles uv ON v.id = uv.vehicle_id
      WHERE uv.user_id = $1
      ORDER BY v.name;
    `;

    const result = await client.query(query, [userId]);
    return result.rows;

  } catch (error) {
    logger.error('Error getting user vehicles:', error);
    throw error;
  } finally {
    client.release();
  }
}

///**
// * Create vehicle and associate with user
// * @param {Object} vehicleData
// * @param {number} userId
// */
async function createVehicleForUser(vehicleData, userId) {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Check if vehicle with this IMEI already exists (more user-friendly)
    const checkQuery = 'SELECT id FROM vehicles WHERE imei = $1';
    const checkResult = await client.query(checkQuery, [vehicleData.imei]);

    if (checkResult.rows.length > 0) {
      throw new Error('Vehicle with this IMEI already exists');
    }

    // Create vehicle
    const createQuery = `
      INSERT INTO vehicles (imei, sim_number, name, description)
      VALUES ($1, $2, $3, $4)
      RETURNING id, imei, sim_number, name, description, created_at;
    `;

    const createResult = await client.query(createQuery, [
      vehicleData.imei,
      vehicleData.sim_number,
      vehicleData.name,
      vehicleData.description || ''
    ]);

    const newVehicle = createResult.rows[0];

    // Associate vehicle with user
    await client.query(
      'INSERT INTO user_vehicles (user_id, vehicle_id) VALUES ($1, $2)',
      [userId, newVehicle.id]
    );

    await client.query('COMMIT');

    logger.info(`Vehicle created and associated with user: ${vehicleData.imei}`);
    return newVehicle;

  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error creating vehicle for user:', error);

    // Re-throw the error so the route handler can catch it properly
    if (error.message === 'Vehicle with this IMEI already exists') {
      throw error;
    }

    throw new Error('Failed to create vehicle');
  } finally {
    client.release();
  }
}

/**
 * Delete vehicle (also removes all associations)
 * @param {number} vehicleId
 */
async function deleteVehicle(vehicleId) {
  const client = await pool.connect();

  try {
    // Check if vehicle exists
    const checkQuery = 'SELECT id FROM vehicles WHERE id = $1';
    const checkResult = await client.query(checkQuery, [vehicleId]);

    if (checkResult.rows.length === 0) {
      throw new Error('Vehicle not found');
    }

    // Delete vehicle (associations will be deleted due to CASCADE)
    const query = 'DELETE FROM vehicles WHERE id = $1';
    await client.query(query, [vehicleId]);

  } catch (error) {
    logger.error('Error deleting vehicle:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = {
  createVehicle,
  getVehicleById,
  getVehicleByImei,
  getVehicleBySimNumber,
  getAllVehicles,
  updateVehicle,
  deleteVehicle,
  associateVehicleWithUser,
  removeVehicleUserAssociation,
  getUserVehicles,
  createVehicleForUser
};
