/**
 * Timezone utility functions
 * Handles timezone conversions consistently across the application
 */

const moment = require('moment-timezone');

/**
 * Convert a date string to UTC for database storage
 * @param {string} dateString - ISO date string from frontend
 * @param {string} sourceTimezone - Source timezone (default: user's local timezone)
 * @returns {Date} UTC Date object for database
 */
function toUTC(dateString, sourceTimezone = null) {
  if (!dateString) return null;

  // If no source timezone specified, assume it's already in local time
  // and convert to UTC
  if (!sourceTimezone) {
    return moment(dateString).toDate();
  }

  return moment.tz(dateString, sourceTimezone).utc().toDate();
}

/**
 * Convert UTC database timestamp to local timezone for display
 * @param {Date|string} utcDate - UTC date from database
 * @param {string} targetTimezone - Target timezone (default: Europe/Rome)
 * @returns {string} Formatted date string in target timezone
 */
function fromUTC(utcDate, targetTimezone = 'Europe/Rome') {
  if (!utcDate) return null;

  return moment.utc(utcDate).tz(targetTimezone).format('YYYY-MM-DDTHH:mm');
}

/**
 * Convert UTC database timestamp to local timezone for display (formatted)
 * @param {Date|string} utcDate - UTC date from database
 * @param {string} targetTimezone - Target timezone (default: Europe/Rome)
 * @returns {string} Formatted date string in target timezone
 */
function formatFromUTC(utcDate, targetTimezone = 'Europe/Rome') {
  if (!utcDate) return null;

  return moment.utc(utcDate).tz(targetTimezone).format('DD/MM/YYYY HH:mm:ss');
}

/**
 * Get current date in user's timezone
 * @param {string} timezone - Target timezone (default: Europe/Rome)
 * @returns {string} Current date in specified timezone
 */
function getCurrentDateInTimezone(timezone = 'Europe/Rome') {
  return moment.tz(timezone).format('YYYY-MM-DD');
}

/**
 * Get start and end of day in UTC for database queries
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @param {string} timezone - User's timezone (default: Europe/Rome)
 * @returns {Object} Object with startOfDayUTC and endOfDayUTC
 */
function getDayBoundsInUTC(dateString, timezone = 'Europe/Rome') {
  if (!dateString) return null;

  const startOfDay = moment.tz(dateString, timezone).startOf('day');
  const endOfDay = moment.tz(dateString, timezone).endOf('day');

  return {
    startOfDayUTC: startOfDay.utc().toDate(),
    endOfDayUTC: endOfDay.utc().toDate()
  };
}

/**
 * Convert local datetime input to UTC range for database queries
 * @param {string} startDateString - Start datetime string
 * @param {string} endDateString - End datetime string
 * @param {string} timezone - User's timezone (default: Europe/Rome)
 * @returns {Object} Object with startUTC and endUTC
 */
function convertDateRangeToUTC(startDateString, endDateString, timezone = 'Europe/Rome') {
  if (!startDateString || !endDateString) return null;

  const startUTC = moment.tz(startDateString, timezone).utc().toDate();
  const endUTC = moment.tz(endDateString, timezone).utc().toDate();

  return {
    startUTC,
    endUTC
  };
}

/**
 * Get user's timezone from request headers
 * @param {Object} req - Express request object
 * @returns {string} User's timezone
 */
function getUserTimezone(req) {
  // Try to get timezone from various headers
  const timezone = req.headers['x-timezone'] ||
                   req.headers['timezone'] ||
                   req.query.timezone ||
                   'Europe/Rome'; // Default to Italy timezone

  return timezone;
}

/**
 * Validate if a date string is in correct format
 * @param {string} dateString - Date string to validate
 * @returns {boolean} True if valid
 */
function isValidDateString(dateString) {
  if (!dateString) return false;
  return moment(dateString, moment.ISO_8601, true).isValid();
}

module.exports = {
  toUTC,
  fromUTC,
  formatFromUTC,
  getCurrentDateInTimezone,
  getDayBoundsInUTC,
  convertDateRangeToUTC,
  getUserTimezone,
  isValidDateString
};
