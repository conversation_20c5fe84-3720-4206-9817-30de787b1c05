/* ECOTrac Design System - CSS Variables Foundation */
:root {
  /* ===== BRAND COLORS ===== */
  --brand-900: #064420;  /* Updated to match design-tokens.json */
  --brand-700: #0b8457;
  --brand-600: #56ab2f;
  --brand-400: #16c79a;
  --brand-light: #a8e6cf;

  /* ===== SEMANTIC COLORS ===== */
  --color-primary: var(--brand-600);
  --color-primary-hover: var(--brand-700);
  --color-primary-dark: var(--brand-900);
  --color-secondary: var(--brand-400);

  /* Status Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: var(--brand-400);

  /* ===== BACKGROUND COLORS ===== */
  --color-background: #ffffff;
  --color-background-light: var(--brand-light);
  --color-surface: #ffffff;
  --color-surface-light: #f8f9fa;

  /* Background Gradients */
  --bg-gradient-start: #c8eccf;
  --bg-gradient-end: #a5d6b4;
  --bg-gradient: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));

  /* ===== TEXT COLORS ===== */
  --color-text-primary: #1a1a1a;
  --color-text-secondary: #4a4a4a;
  --color-text-muted: #6b7280;
  --color-text-on-dark: #ffffff;
  --color-text-on-primary: #ffffff;

  /* ===== BORDER COLORS ===== */
  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
  --color-border-focus: var(--brand-400);

  /* ===== SHADOWS ===== */
  --shadow-sm: 0 1px 2px 0 rgba(6, 68, 32, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(6, 68, 32, 0.1), 0 2px 4px -1px rgba(6, 68, 32, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(6, 68, 32, 0.1), 0 4px 6px -2px rgba(6, 68, 32, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(6, 68, 32, 0.1), 0 10px 10px -5px rgba(6, 68, 32, 0.04);

  /* Enhanced shadows for specific components */
  --shadow-header: 0 4px 20px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1), 0 0 40px rgba(0, 0, 0, 0.05);
  --shadow-panel: -4px 0 20px rgba(0, 0, 0, 0.15), 0 4px 20px rgba(0, 0, 0, 0.1), 0 0 40px rgba(0, 0, 0, 0.05);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.06), 0 0 20px rgba(0, 0, 0, 0.04);

  /* ===== FOCUS STATES ===== */
  --focus-ring: rgba(22, 199, 154, 0.5);
  --focus-ring-width: 3px;
  --focus-ring-offset: 2px;

  /* ===== SPACING SYSTEM ===== */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */

  /* ===== TYPOGRAPHY SYSTEM ===== */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* ===== BORDER RADIUS ===== */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;  /* Full circle */

  /* ===== TRANSITIONS ===== */
  --transition-fast: 150ms ease;
  --transition-normal: 200ms ease;
  --transition-slow: 300ms ease;
  --transition-all: all var(--transition-normal);

  /* ===== Z-INDEX SCALE ===== */
  --z-dropdown: 100;
  --z-sticky: 200;
  --z-fixed: 300;
  --z-modal-backdrop: 400;
  --z-modal: 500;
  --z-popover: 600;
  --z-tooltip: 700;
  --z-toast: 9999;
}

/* ===== UTILITY CLASSES ===== */

/* Color Utilities */
.text-brand-900 { color: var(--brand-900); }
.text-brand-700 { color: var(--brand-700); }
.text-brand-600 { color: var(--brand-600); }
.text-brand-400 { color: var(--brand-400); }
.text-brand-light { color: var(--brand-light); }
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-on-dark { color: var(--color-text-on-dark); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-brand-900 { background-color: var(--brand-900); }
.bg-brand-700 { background-color: var(--brand-700); }
.bg-brand-600 { background-color: var(--brand-600); }
.bg-brand-400 { background-color: var(--brand-400); }
.bg-brand-light { background-color: var(--brand-light); }
.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-surface { background-color: var(--color-surface); }
.bg-surface-light { background-color: var(--color-surface-light); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }
.bg-gradient { background: var(--bg-gradient); }

.border-brand-900 { border-color: var(--brand-900); }
.border-brand-700 { border-color: var(--brand-700); }
.border-brand-600 { border-color: var(--brand-600); }
.border-brand-400 { border-color: var(--brand-400); }
.border-default { border-color: var(--color-border); }
.border-light { border-color: var(--color-border-light); }
.border-focus { border-color: var(--color-border-focus); }
