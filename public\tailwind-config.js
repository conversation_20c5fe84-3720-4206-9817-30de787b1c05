// Configurazione Tailwind CSS per palette verde ECOTrac
module.exports = {
  content: [
    "./public/**/*.{html,js}",
    "./src/**/*.{html,js,jsx,ts,tsx,vue}",
  ],
  theme: {
    extend: {
      colors: {
        // Brand Colors - Palette Verde ECOTrac
        brand: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#16c79a',  // brand-400
          500: '#10b981',
          600: '#56ab2f',  // brand-600
          700: '#0b8457',  // brand-700
          800: '#166534',
          900: '#064420',  // brand-900
          950: '#052e16',
          light: '#a8e6cf'  // bg-light
        },

        // Semantic colors basati sulla palette
        primary: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#16c79a',
          500: '#10b981',
          600: '#56ab2f',  // primary
          700: '#0b8457',  // primary-hover
          800: '#166534',
          900: '#064420',  // primary-dark
          950: '#052e16'
        },

        // Status colors
        success: '#10b981',  // Verde diverso dal brand per stati positivi
        warning: '#f59e0b',
        error: '#ef4444',    // Rosso per errori
        info: '#16c79a',

        // Text colors ottimizzati per accessibilità
        text: {
          primary: '#1a1a1a',    // 13.2:1 su bg-light
          secondary: '#4a4a4a',  // 8.9:1 su bg-light
          muted: '#6b7280'       // 5.6:1 su bg-light
        },

        // Background colors
        background: {
          primary: '#ffffff',
          light: '#a8e6cf',      // Verde chiaro per hero
          surface: '#ffffff',
          'surface-light': '#f8f9fa'
        }
      },

      // Spacing system
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '112': '28rem',
        '128': '32rem'
      },

      // Typography
      fontFamily: {
        sans: [
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Helvetica',
          'Arial',
          'sans-serif'
        ],
        mono: [
          'Monaco',
          'Cascadia Code',
          'Roboto Mono',
          'Consolas',
          'Courier New',
          'monospace'
        ]
      },

      // Border radius
      borderRadius: {
        '4xl': '2rem',
        '5xl': '2.5rem'
      },

      // Shadows basati sui colori verdi
      boxShadow: {
        'brand-sm': '0 1px 2px 0 rgba(6, 68, 32, 0.05)',
        'brand-md': '0 4px 6px -1px rgba(6, 68, 32, 0.1), 0 2px 4px -1px rgba(6, 68, 32, 0.06)',
        'brand-lg': '0 10px 15px -3px rgba(6, 68, 32, 0.1), 0 4px 6px -2px rgba(6, 68, 32, 0.05)',
        'brand-xl': '0 20px 25px -5px rgba(6, 68, 32, 0.1), 0 10px 10px -5px rgba(6, 68, 32, 0.04)'
      },

      // Focus ring semitrasparente
      ringColor: {
        'brand-focus': 'rgba(22, 199, 154, 0.5)'
      }
    }
  },

  plugins: [
    // Plugin per focus-visible
    function({ addUtilities }) {
      addUtilities({
        '.focus-visible\\:ring-brand-focus:focus-visible': {
          '--tw-ring-color': 'rgba(22, 199, 154, 0.5)',
          '--tw-ring-opacity': '1'
        }
      })
    }
  ]
}

// Classi utility personalizzate per il progetto
const brandUtilities = {
  '.btn-primary': {
    backgroundColor: '#56ab2f',  // brand-600
    color: '#ffffff',
    padding: '0.5rem 1rem',
    borderRadius: '0.375rem',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    border: 'none',
    cursor: 'pointer'
  },
  '.btn-primary:hover': {
    backgroundColor: '#0b8457'  // brand-700
  },
  '.btn-primary:focus': {
    outline: '3px solid rgba(22, 199, 154, 0.5)',
    outlineOffset: '2px'
  },

  '.btn-secondary': {
    backgroundColor: '#16c79a',  // brand-400
    color: '#ffffff',
    padding: '0.5rem 1rem',
    borderRadius: '0.375rem',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    border: 'none',
    cursor: 'pointer'
  },
  '.btn-secondary:hover': {
    backgroundColor: '#56ab2f'  // brand-600
  },

  '.btn-ghost': {
    backgroundColor: 'transparent',
    color: '#064420',  // brand-900
    padding: '0.5rem 1rem',
    borderRadius: '0.375rem',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    border: '1px solid #064420'
  },
  '.btn-ghost:hover': {
    backgroundColor: '#064420',
    color: '#ffffff'
  },

  '.text-gradient': {
    background: 'linear-gradient(135deg, #a8e6cf, #56ab2f)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text'
  },

  '.bg-hero': {
    background: 'linear-gradient(135deg, #a8e6cf, #56ab2f)'
  },

  '.nav-pill': {
    position: 'relative',
    transition: 'all 0.2s ease'
  },
  '.nav-pill::after': {
    content: '""',
    position: 'absolute',
    bottom: '-2px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '20px',
    height: '2px',
    backgroundColor: '#0b8457',
    borderRadius: '1px'
  }
}

// Esporta anche le utility per uso programmatico
module.exports.brandUtilities = brandUtilities;
