# 🕐 Gestione Timezone - Documentazione Completa

## 📋 Panoramica

Questo progetto implementa una gestione avanzata dei fusi orari per garantire che tutti i timestamp vengano gestiti correttamente indipendentemente dal timezone dell'utente o del dispositivo.

## 🔧 Problema Risolto

**Problema originale:**
- L'utente selezionava le 23:10 del giorno corrente per vedere i messaggi
- Doveva impostare la data di fine alle 02:00 del giorno successivo
- Conversione non corretta tra timezone locale e UTC

**Soluzione implementata:**
- Utility centralizzata per la gestione dei timezone
- Conversione automatica tra timezone dell'utente e UTC
- Supporto per timezone configurabile (default: Europe/Rome)

## 🏗️ Architettura

### 1. **Utility Timezone** (`src/utils/timezone.js`)
```javascript
const { convertDateRangeToUTC } = require('../utils/timezone');
const { getUserTimezone } = require('../utils/timezone');
```

### 2. **Backend** (`src/routes/admin.js`)
- Conversione automatica delle date dal frontend
- Utilizzo del timezone dell'utente per le query database

### 3. **Database** (`src/services/gpsService.js`)
- Salvataggio consistente dei timestamp in UTC
- Query ottimizzate per range di date

### 4. **Frontend** (`public/vehicle-messages.html`)
- Input datetime-local per una migliore UX
- Conversione corretta per le API calls

## 📅 Come Funziona

### Flusso Completo:

1. **Utente seleziona date nel frontend:**
   - Data inizio: `2024-01-15T23:00`
   - Data fine: `2024-01-16T01:59`

2. **Frontend invia richiesta API:**
   ```javascript
   const startDate = new Date(startDateInput.value);
   params.append('startDate', startDate.toISOString());
   ```

3. **Backend riceve e converte:**
   ```javascript
   const userTimezone = getUserTimezone(req); // "Europe/Rome"
   const dateRange = convertDateRangeToUTC(startDate, endDate, userTimezone);
   ```

4. **Database query:**
   ```sql
   WHERE device_timestamp >= '2024-01-15T22:00:00.000Z'
   AND device_timestamp <= '2024-01-16T00:59:00.000Z'
   ```

## ⚙️ Configurazione

### Variabile d'Ambiente
```env
# Nel file .env
USER_TIMEZONE=Europe/Rome
```

### Header HTTP
```javascript
// Nel frontend
headers: {
  'X-Timezone': 'Europe/Rome'
}
```

### Query Parameter
```javascript
// Nel frontend
fetch(`/api/vehicles/${vehicleId}/messages?timezone=Europe/Rome`)
```

## 🛠️ Utilizzo

### Backend - Conversione Date
```javascript
const { convertDateRangeToUTC, getUserTimezone } = require('../utils/timezone');

// In un route handler
const userTimezone = getUserTimezone(req);
const dateRange = convertDateRangeToUTC(req.query.startDate, req.query.endDate, userTimezone);

const options = {
  startDate: dateRange.startUTC,
  endDate: dateRange.endUTC
};
```

### Frontend - Input DateTime
```html
<input type="datetime-local" id="startDate" onchange="validateDateRange()">
<input type="datetime-local" id="endDate" onchange="validateDateRange()">
```

### Database - Timestamp Storage
```javascript
// I timestamp vengono sempre salvati in UTC
const deviceTimestamp = new Date(gpsData.device_timestamp); // Già in UTC
```

## 🌍 Timezone Supportati

Il sistema supporta tutti i timezone standard di moment-timezone:

- **Europe/Rome** (Italia) - Default
- **Europe/London** (UK)
- **America/New_York** (USA East)
- **Asia/Tokyo** (Giappone)
- **Australia/Sydney** (Australia)

## 🔍 Debug e Troubleshooting

### Verifica Conversione Timezone
```javascript
const { convertDateRangeToUTC } = require('../utils/timezone');

const result = convertDateRangeToUTC(
  '2024-01-15T23:00',
  '2024-01-16T01:59',
  'Europe/Rome'
);

console.log(result);
// Output:
// {
//   startUTC: 2024-01-15T22:00:00.000Z,
//   endUTC: 2024-01-16T00:59:00.000Z
// }
```

### Log Timezone
```javascript
const { getUserTimezone } = require('../utils/timezone');

console.log('User timezone:', getUserTimezone(req));
```

## 📱 Frontend - Migliori Pratiche

### 1. **Input DateTime-Local**
```html
<input type="datetime-local" id="startDate">
```
- Mostra automaticamente il formato locale dell'utente
- Gestisce automaticamente il cambio ora legale/solare

### 2. **Validazione Date**
```javascript
function validateDateRange() {
  const startDate = new Date(document.getElementById('startDate').value);
  const endDate = new Date(document.getElementById('endDate').value);

  if (startDate >= endDate) {
    showToastError('La data di inizio deve essere precedente alla data di fine');
    return false;
  }
  return true;
}
```

### 3. **Timezone Header**
```javascript
// Includere sempre l'header timezone nelle richieste API
headers: {
  'Authorization': `Bearer ${token}`,
  'X-Timezone': 'Europe/Rome'
}
```

## 🗄️ Database - Considerazioni

### Timestamp Storage
- **device_timestamp**: Sempre in UTC (dal dispositivo GPS)
- **server_timestamp**: Sempre in UTC (quando ricevuto dal server)
- **created_at/updated_at**: Sempre in UTC (PostgreSQL)

### Query Optimization
```sql
-- Query ottimizzata con timezone conversion
SELECT *
FROM gps_data
WHERE device_timestamp >= $1::timestamptz
  AND device_timestamp <= $2::timestamptz
ORDER BY device_timestamp DESC;
```

## 🚀 Deployment

### Variabili d'Ambiente
```env
# .env
USER_TIMEZONE=Europe/Rome
DATABASE_TIMEZONE=UTC
```

### Docker
```dockerfile
# Dockerfile
ENV USER_TIMEZONE=Europe/Rome
ENV DATABASE_TIMEZONE=UTC
```

## 🔧 Testing

### Test Unitari
```javascript
// test/timezone.test.js
const { convertDateRangeToUTC } = require('../src/utils/timezone');

test('should convert Italy timezone correctly', () => {
  const result = convertDateRangeToUTC(
    '2024-01-15T23:00',
    '2024-01-16T01:59',
    'Europe/Rome'
  );

  expect(result.startUTC).toBe('2024-01-15T22:00:00.000Z');
  expect(result.endUTC).toBe('2024-01-16T00:59:00.000Z');
});
```

### Test Integrazione
```javascript
// test/api.test.js
test('should handle timezone conversion in API', async () => {
  const response = await request(app)
    .get('/api/admin/vehicles/1/messages')
    .query({
      startDate: '2024-01-15T23:00',
      endDate: '2024-01-16T01:59'
    })
    .set('X-Timezone', 'Europe/Rome');

  expect(response.status).toBe(200);
  expect(response.body.data.length).toBeGreaterThan(0);
});
```

## 📊 Monitoraggio

### Log Timezone
```javascript
// In qualsiasi punto del codice
const logger = require('./utils/logger');
logger.info('Processing request with timezone:', getUserTimezone(req));
```

### Metriche Performance
- Monitorare i tempi di conversione timezone
- Verificare la correttezza delle query database
- Controllare la consistenza dei dati

## 🔄 Migrazione

Se hai dati esistenti nel database, potrebbe essere necessario:

1. **Verificare i timestamp esistenti:**
   ```sql
   SELECT COUNT(*) FROM gps_data WHERE device_timestamp IS NULL;
   ```

2. **Aggiornare timestamp nulli:**
   ```sql
   UPDATE gps_data SET device_timestamp = server_timestamp WHERE device_timestamp IS NULL;
   ```

3. **Verificare la consistenza:**
   ```sql
   SELECT imei, COUNT(*) as total_messages
   FROM gps_data
   GROUP BY imei
   ORDER BY total_messages DESC;
   ```

## 🎯 Risultato Finale

Con questa implementazione:

✅ **L'utente seleziona 23:10** → Vede i messaggi delle 23:10
✅ **Non più necessario** impostare le 02:00 del giorno successivo
✅ **Gestione automatica** del cambio ora legale/solare
✅ **Supporto multi-timezone** per utenti in paesi diversi
✅ **Conversione consistente** tra frontend, backend e database

Il sistema ora gestisce correttamente i fusi orari in tutto il progetto! 🌍✨
