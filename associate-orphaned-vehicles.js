#!/usr/bin/env node

/**
 * <PERSON>ript to associate orphaned vehicles with the admin user
 *
 * This script finds all vehicles that are not associated with any user
 * and associates them with the 'admin' user account.
 *
 * Usage: node associate-orphaned-vehicles.js
 */

const pool = require('./src/config/database');
const logger = require('./src/utils/logger');

async function associateOrphanedVehicles() {
  const client = await pool.connect();

  try {
    logger.info('Starting orphaned vehicle association process...');

    // Find the admin user
    const adminQuery = 'SELECT id, username FROM users WHERE username = $1';
    const adminResult = await client.query(adminQuery, ['admin']);

    if (adminResult.rows.length === 0) {
      logger.error('Admin user not found! Please ensure an admin user exists.');
      return;
    }

    const adminUser = adminResult.rows[0];
    logger.info(`Found admin user: ${adminUser.username} (ID: ${adminUser.id})`);

    // Find orphaned vehicles (vehicles not associated with any user)
    const orphanedQuery = `
      SELECT v.id, v.imei, v.name, v.description
      FROM vehicles v
      WHERE v.id NOT IN (
        SELECT DISTINCT uv.vehicle_id
        FROM user_vehicles uv
        WHERE uv.vehicle_id IS NOT NULL
      )
    `;

    const orphanedResult = await client.query(orphanedQuery);

    if (orphanedResult.rows.length === 0) {
      logger.info('No orphaned vehicles found. All vehicles are already associated with users.');
      return;
    }

    logger.info(`Found ${orphanedResult.rows.length} orphaned vehicles to associate:`);

    // Display orphaned vehicles
    orphanedResult.rows.forEach((vehicle, index) => {
      logger.info(`${index + 1}. IMEI: ${vehicle.imei}, Name: ${vehicle.name || 'N/A'}`);
    });

    // Associate each orphaned vehicle with admin user
    let successCount = 0;
    let errorCount = 0;

    for (const vehicle of orphanedResult.rows) {
      try {
        const associateQuery = `
          INSERT INTO user_vehicles (user_id, vehicle_id)
          VALUES ($1, $2)
          ON CONFLICT (user_id, vehicle_id) DO NOTHING
        `;

        await client.query(associateQuery, [adminUser.id, vehicle.id]);
        logger.info(`✓ Associated vehicle IMEI ${vehicle.imei} with admin user`);
        successCount++;

      } catch (error) {
        logger.error(`✗ Failed to associate vehicle IMEI ${vehicle.imei}:`, error.message);
        errorCount++;
      }
    }

    // Summary
    logger.info(`\n=== Association Summary ===`);
    logger.info(`Successfully associated: ${successCount} vehicles`);
    logger.info(`Failed to associate: ${errorCount} vehicles`);
    logger.info(`Total processed: ${orphanedResult.rows.length} vehicles`);

    if (successCount > 0) {
      logger.info(`\nAll orphaned vehicles are now associated with user: ${adminUser.username}`);
    }

  } catch (error) {
    logger.error('Error during orphaned vehicle association:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the script
if (require.main === module) {
  associateOrphanedVehicles()
    .then(() => {
      logger.info('Orphaned vehicle association process completed.');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { associateOrphanedVehicles };
