const fs = require('fs');
const path = require('path');
const pool = require('../config/database');

async function runMigrations() {
  try {
    console.log('Running database migrations...');
    
    // Read the schema file
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute the schema
    await pool.query(schema);

    // Check and update sim_number column in vehicles table
    try {
      const checkColumn = await pool.query(`
        SELECT column_name, is_nullable, data_type
        FROM information_schema.columns
        WHERE table_name = 'vehicles' AND column_name = 'sim_number'
      `);

      if (checkColumn.rows.length === 0) {
        console.log('Adding sim_number column to vehicles table...');
        await pool.query(`
          ALTER TABLE vehicles ADD COLUMN sim_number VARCHAR(20) UNIQUE NOT NULL DEFAULT ''
        `);
        console.log('✓ Added sim_number column to vehicles table');
      } else {
        const column = checkColumn.rows[0];
        console.log('Updating sim_number column constraints...');

        if (column.is_nullable === 'YES') {
          // First set default values for existing NULL records
          await pool.query(`
            UPDATE vehicles SET sim_number = CONCAT('TEMP-', id) WHERE sim_number IS NULL OR sim_number = ''
          `);
          console.log('✓ Updated existing NULL/empty sim_number values');

          // Then make column NOT NULL
          await pool.query(`
            ALTER TABLE vehicles ALTER COLUMN sim_number SET NOT NULL
          `);
          console.log('✓ Set sim_number column to NOT NULL');
        }

        // Check for unique constraint
        try {
          const uniqueCheck = await pool.query(`
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'vehicles'
              AND constraint_type = 'UNIQUE'
              AND constraint_name LIKE '%sim_number%'
          `);

          if (uniqueCheck.rows.length === 0) {
            await pool.query(`
              ALTER TABLE vehicles ADD CONSTRAINT vehicles_sim_number_unique UNIQUE (sim_number)
            `);
            console.log('✓ Added unique constraint to sim_number column');
          } else {
            console.log('✓ Unique constraint already exists for sim_number');
          }
        } catch (uniqueError) {
          console.log('Note: Could not add unique constraint:', uniqueError.message);
        }
      }

      // Update IMEI CHECK constraint
      try {
        const checkConstraints = await pool.query(`
          SELECT constraint_name
          FROM information_schema.table_constraints
          WHERE table_name = 'vehicles'
            AND constraint_type = 'CHECK'
            AND constraint_name LIKE '%imei%'
        `);

        if (checkConstraints.rows.length === 0) {
          await pool.query(`
            ALTER TABLE vehicles ADD CONSTRAINT vehicles_imei_check CHECK (imei ~ '^[0-9]{7,15}$')
          `);
          console.log('✓ Added IMEI numeric validation constraint');
        } else {
          console.log('✓ IMEI constraint already exists');
        }
      } catch (checkError) {
        console.log('Note: Could not add IMEI check constraint:', checkError.message);
      }

    } catch (columnCheckError) {
      console.log('Note: Could not check/update sim_number column:', columnCheckError.message);
    }

    // Update gps_data table schema to match new specification
    try {
      const checkTable = await pool.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_name = 'gps_data' AND table_schema = 'public'
      `);

      if (checkTable.rows.length > 0) {
        console.log('Updating gps_data table schema...');

        // Remove obsolete columns if they exist
        const columnsToRemove = ['power_voltage', 'odometer'];
        for (const column of columnsToRemove) {
          try {
            const columnCheck = await pool.query(`
              SELECT column_name
              FROM information_schema.columns
              WHERE table_name = 'gps_data' AND column_name = $1
            `, [column]);

            if (columnCheck.rows.length > 0) {
              await pool.query(`ALTER TABLE gps_data DROP COLUMN ${column}`);
              console.log(`✓ Removed obsolete ${column} column from gps_data table`);
            }
          } catch (columnError) {
            console.log(`Note: Could not remove ${column} column: ${columnError.message}`);
          }
        }

        // Ensure columns exist with correct specifications
        const columnsToAdd = [
          { name: 'battery_voltage', type: 'DECIMAL(5, 1)', default: '0.0' },
          { name: 'total_odometer', type: 'DECIMAL(10, 1)', default: '0.0' },
          { name: 'external_voltage', type: 'DECIMAL(5, 1)', default: '0.0' }
        ];

        for (const col of columnsToAdd) {
          const columnCheck = await pool.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'gps_data' AND column_name = $1
          `, [col.name]);

          if (columnCheck.rows.length === 0) {
            await pool.query(`
              ALTER TABLE gps_data ADD COLUMN ${col.name} ${col.type} DEFAULT ${col.default}
            `);
            console.log(`✓ Added ${col.name} column to gps_data table`);
          }
        }

        // Update column precisions if needed
        for (const col of columnsToAdd) {
          try {
            const currentTypeQuery = await pool.query(`
              SELECT data_type, numeric_precision, numeric_scale
              FROM information_schema.columns
              WHERE table_name = 'gps_data' AND column_name = $1
            `, [col.name]);

            if (currentTypeQuery.rows.length > 0) {
              const { numeric_scale } = currentTypeQuery.rows[0];
              const expectedScale = col.type.includes('(10, 1)') ? 1 : col.type.includes('(5, 1)') ? 1 : null;
              if (expectedScale !== null && numeric_scale !== expectedScale) {
                await pool.query(`
                  ALTER TABLE gps_data ALTER COLUMN ${col.name} TYPE ${col.type}
                `);
                console.log(`✓ Updated ${col.name} column precision to ${col.type}`);
              }
            }
          } catch (typeError) {
            console.log(`Note: Could not check/alter ${col.name} precision:`, typeError.message);
          }
        }
      } else {
        console.log('gps_data table does not exist yet, will be created when schema is applied');
      }

    } catch (schemaError) {
      console.log('Note: Could not update gps_data table schema:', schemaError.message);
    }

    console.log('Database migrations completed successfully!');
    
    // Test the connection and show table info
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);
    
    console.log('Created tables:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
  } catch (error) {
    console.error('Error running migrations:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations();
}

module.exports = runMigrations;
