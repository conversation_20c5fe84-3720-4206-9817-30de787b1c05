/* ECOTrac Design System - Enhanced Form Components */
/* This file extends the base form components from components.css */

/* Legacy form-group support (maintains backward compatibility) */
.form-group {
    margin-bottom: var(--space-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    transition: var(--transition-all);
    background-color: var(--color-background);
    color: var(--color-text-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 2px var(--focus-ring);
}

.form-group textarea {
    resize: vertical;
    min-height: calc(var(--space-md) * 3);
}

/* Enhanced filter components */
.filters {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.filters input,
.filters select {
    padding: var(--space-xs) var(--space-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    min-width: 15px;
    font-family: var(--font-family-primary);
    transition: var(--transition-all);
    background-color: var(--color-background);
    color: var(--color-text-primary);
}

.filters input:focus,
.filters select:focus {
    outline: none;
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 2px var(--focus-ring);
}

/* Form validation states */
.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
    border-color: var(--color-error);
}

.form-group.has-error input:focus,
.form-group.has-error select:focus,
.form-group.has-error textarea:focus {
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.form-group.has-success input,
.form-group.has-success select,
.form-group.has-success textarea {
    border-color: var(--color-success);
}

.form-group.has-success input:focus,
.form-group.has-success select:focus,
.form-group.has-success textarea:focus {
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.form-error-message {
    color: var(--color-error);
    font-size: var(--font-size-xs);
    margin-top: var(--space-xs);
    display: block;
}

.form-success-message {
    color: var(--color-success);
    font-size: var(--font-size-xs);
    margin-top: var(--space-xs);
    display: block;
}

.form-help-text {
    color: var(--color-text-muted);
    font-size: var(--font-size-xs);
    margin-top: var(--space-xs);
    display: block;
}

/* Form input groups */
.input-group {
    display: flex;
    align-items: stretch;
}

.input-group input {
    border-radius: 0;
    border-right: 0;
}

.input-group input:first-child {
    border-top-left-radius: var(--radius-sm);
    border-bottom-left-radius: var(--radius-sm);
}

.input-group input:last-child {
    border-top-right-radius: var(--radius-sm);
    border-bottom-right-radius: var(--radius-sm);
    border-right: 1px solid var(--color-border);
}

.input-group .btn {
    border-radius: 0;
    border-left: 0;
}

.input-group .btn:last-child {
    border-top-right-radius: var(--radius-sm);
    border-bottom-right-radius: var(--radius-sm);
}

/* Checkbox and radio styling */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-sm);
}

.form-check input[type="checkbox"],
.form-check input[type="radio"] {
    width: auto;
    margin: 0;
}

.form-check label {
    margin: 0;
    font-weight: var(--font-weight-normal);
    cursor: pointer;
}

/* File input styling */
.form-file {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.form-file input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.form-file-label {
    display: inline-block;
    padding: var(--space-sm) var(--space-md);
    background: var(--color-surface-light);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-all);
}

.form-file:hover .form-file-label {
    background: var(--color-primary);
    color: white;
}

/* Note: Button styles are now handled by components.css */
/* This maintains backward compatibility while using the new design system */
