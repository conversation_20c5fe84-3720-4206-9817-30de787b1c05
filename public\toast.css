/* ECOTrac Design System - Toast Notifications */
.toast-container {
    position: fixed;
    bottom: var(--space-md);
    right: var(--space-md);
    z-index: var(--z-toast);
    max-width: 350px;
}

.toast {
    background: var(--color-surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--space-sm);
    padding: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    border-left: 4px solid;
    opacity: 0;
    transform: translateX(100%);
    transition: var(--transition-slow);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.hide {
    opacity: 0;
    transform: translateX(100%);
}

.toast-success {
    border-left-color: var(--color-success);
}

.toast-error {
    border-left-color: var(--color-error);
}

.toast-info {
    border-left-color: var(--color-info);
}

.toast-warning {
    border-left-color: var(--color-warning);
}

.toast-confirm {
    border-left-color: var(--color-warning);
}

.toast-confirm .toast-message {
    margin-bottom: var(--space-sm);
}

.toast-confirm .toast-actions {
    display: flex;
    gap: var(--space-sm);
    justify-content: flex-end;
}

.toast-confirm .btn-confirm,
.toast-confirm .btn-cancel {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition-all);
}

.toast-confirm .btn-confirm {
    background: var(--color-error);
    color: white;
}

.toast-confirm .btn-confirm:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.toast-confirm .btn-cancel {
    background: var(--color-text-muted);
    color: white;
}

.toast-confirm .btn-cancel:hover {
    background: var(--color-text-secondary);
    transform: translateY(-1px);
}

.toast-icon {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.toast-message {
    flex: 1;
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    color: var(--color-text-primary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--color-text-muted);
    cursor: pointer;
    font-size: var(--font-size-xl);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border-radius: var(--radius-full);
    transition: var(--transition-all);
}

.toast-close:hover {
    background-color: var(--color-surface-light);
    color: var(--color-text-primary);
}
