const jwt = require('jsonwebtoken');
const { getUserById, hasVehicleAccess } = require('../services/userService');
const logger = require('../utils/logger');

/**
 * Middleware to authenticate JWT token
 */
async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get full user data
    const user = await getUserById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    // Add user to request object with helper methods
    req.user = {
      ...user,
      hasVehicleAccess: async (imei) => {
        if (user.account_type === 'admin') return true;
        return await hasVehicleAccess(user.id, imei);
      },
      getVehicleImeis: async () => {
        const { getUserVehicles } = require('../services/userService');
        const vehicles = await getUserVehicles(user.id);
        return vehicles.map(v => v.imei);
      }
    };
    
    next();
    
  } catch (error) {
    logger.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Authentication failed'
    });
  }
}

/**
 * Middleware to check if user is admin
 */
function requireAdmin(req, res, next) {
  if (!req.user || req.user.account_type !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  next();
}

/**
 * Middleware to validate vehicle access
 */
async function authorizeVehicleAccess(req, res, next) {
  try {
    const imei = req.query.imei || req.params.imei;
    
    if (!imei) {
      return res.status(400).json({
        success: false,
        message: 'IMEI required'
      });
    }
    
    const hasAccess = await req.user.hasVehicleAccess(imei);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this vehicle'
      });
    }
    
    next();
    
  } catch (error) {
    logger.error('Authorization error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authorization failed'
    });
  }
}

module.exports = {
  authenticateToken,
  requireAdmin,
  authorizeVehicleAccess
};
