/* ECOTrac Design System - Master CSS File */
/* Import this single file to get the complete design system */

/* ===== DESIGN SYSTEM FOUNDATION ===== */
@import url('./color-system.css');

/* ===== CORE COMPONENTS ===== */
@import url('./components.css');

/* ===== UTILITY CLASSES ===== */
@import url('./utilities.css');

/* ===== ENHANCED FORM COMPONENTS ===== */
@import url('./forms.css');

/* ===== TOAST NOTIFICATIONS ===== */
@import url('./toast.css');

/* ===== GLOBAL OVERRIDES & ENHANCEMENTS ===== */

/* Ensure consistent box-sizing across all elements */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Enhanced body defaults */
body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background: var(--bg-gradient);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced focus styles for accessibility */
*:focus {
  outline: var(--focus-ring-width) solid var(--focus-ring);
  outline-offset: var(--focus-ring-offset);
}

/* Remove focus outline for mouse users but keep for keyboard users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced link styles */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-all);
}

a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

/* Enhanced heading styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin: 0 0 var(--space-md) 0;
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* Enhanced paragraph styles */
p {
  margin: 0 0 var(--space-md) 0;
  line-height: var(--line-height-normal);
}

/* Enhanced list styles */
ul, ol {
  margin: 0 0 var(--space-md) 0;
  padding-left: var(--space-xl);
}

li {
  margin-bottom: var(--space-xs);
}

/* Enhanced table styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--space-md);
}

th, td {
  padding: var(--space-sm);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
}

th {
  font-weight: var(--font-weight-semibold);
  background-color: var(--color-surface-light);
  color: var(--color-text-primary);
}

/* Enhanced code styles */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--color-surface-light);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  color: var(--color-text-primary);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--color-surface-light);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin-bottom: var(--space-md);
}

pre code {
  background: none;
  padding: 0;
}

/* Enhanced blockquote styles */
blockquote {
  margin: var(--space-md) 0;
  padding: var(--space-md);
  border-left: 4px solid var(--color-primary);
  background-color: var(--color-surface-light);
  font-style: italic;
}

/* Enhanced hr styles */
hr {
  border: none;
  height: 1px;
  background-color: var(--color-border);
  margin: var(--space-xl) 0;
}

/* Enhanced selection styles */
::selection {
  background-color: var(--focus-ring);
  color: var(--color-text-primary);
}

/* Enhanced scrollbar styles for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface-light);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}

/* Enhanced print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  .header,
  .footer,
  .sidebar,
  .nav-column {
    display: none !important;
  }
  
  .main-content {
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
  }
}

/* Enhanced reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Enhanced high contrast support */
@media (prefers-contrast: high) {
  :root {
    --color-border: #000000;
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
  }
}

/* Enhanced dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
  /* Dark mode variables would go here */
  /* Currently maintaining light theme as per brand requirements */
}

/* ===== COMPONENT LAYOUT HELPERS ===== */

/* Main application layout */
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-header {
  flex-shrink: 0;
}

.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.app-footer {
  flex-shrink: 0;
}

/* Content area helpers */
.content-area {
  flex: 1;
  overflow: auto;
  padding: var(--space-md);
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Login layout helper */
.login-layout {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-gradient);
}

.login-card {
  max-width: 400px;
  width: 100%;
  margin: var(--space-xl);
  background: var(--color-surface);
  padding: var(--space-2xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

/* Map layout helpers */
.map-layout {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 50px;
  display: flex;
}

.map-sidebar-area {
  position: relative;
  z-index: var(--z-fixed);
}

.map-content-area {
  flex: 1;
  position: relative;
  z-index: 1;
}

/* Animation helpers */
.fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.slide-in-right {
  animation: slideInRight var(--transition-slow) ease-out;
}

.slide-in-left {
  animation: slideInLeft var(--transition-slow) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}
