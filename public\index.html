<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tracker GPS Teltonika - Mappa Live</title>
    <link href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    <link rel="stylesheet" href="toast.css">
    <link rel="stylesheet" href="color-system.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));
            height: 100vh;
            overflow: hidden;
        }

        /* Header identico alla pagina admin */
        .header {
            background: var(--brand-900);
            color: var(--color-text-on-dark);
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.15),
                0 2px 8px rgba(0, 0, 0, 0.1),
                0 0 40px rgba(0, 0, 0, 0.05);
            height: 80px;
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            width: 316px;
            height: 60px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            background: var(--color-primary);
            color: var(--color-text-on-primary);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn:focus {
            outline: 3px solid var(--focus-ring);
            outline-offset: 2px;
        }

        .btn-danger {
            background: var(--color-error);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Layout principale */
        .main-content {
            position: fixed;
            top: 80px;
            left: 0;
            right: 0;
            bottom: 50px;
            display: flex;
        }

        /* Colonna sinistra con barra di selezione */
        .left-column {
            width: 60px;
            background: var(--color-surface);
            border-right: 1px solid var(--color-border);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem 0;
            gap: 1rem;
            z-index: 300;
        }

        .selection-btn {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            border: none;
            background: var(--color-surface);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
            color: var(--color-text-primary);
        }

        .selection-btn:hover {
            background: var(--brand-600);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .selection-btn.active {
            background: var(--brand-600);
            color: white;
        }

        /* Sidebar laterali che si aprono verso destra */
        .sidebar {
            position: fixed;
            top: 80px;
            left: 60px;
            width: 320px;
            height: calc(100vh - 140px);
            background: var(--color-surface);
            border-right: 2px solid var(--color-border);
            border-bottom: 1px solid var(--color-border);
            box-shadow:
                -4px 0 20px rgba(0, 0, 0, 0.15),
                0 4px 20px rgba(0, 0, 0, 0.1),
                0 0 40px rgba(0, 0, 0, 0.05);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            z-index: 200;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        .sidebar.open {
            transform: translateX(0);
        }

        .sidebar-header {
            background: var(--color-background-light);
            padding: 1rem;
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--color-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-header h3 {
            color: var(--color-text-primary);
            margin: 0;
            font-size: 1.1rem;
        }

        .close-sidebar {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--color-text-secondary);
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .close-sidebar:hover {
            background: var(--color-error);
            color: white;
        }

        .sidebar-content {
            padding: 1rem;
        }

        /* Colonna destra con mappa */
        .right-column {
            flex: 1;
            position: relative;
            z-index: 1;
        }

        .map-container {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 1;
        }

        #map {
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        /* Footer fisso in basso */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: var(--color-surface);
            border-top: 1px solid var(--color-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 100;
        }

        .footer-info {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--color-text-secondary);
            font-size: 0.9rem;
        }

        .info-item .icon {
            font-size: 1.2rem;
        }

        .info-item .value {
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .footer-expand-btn {
            background: var(--brand-600);
            color: white;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .footer-expand-btn:hover {
            background: var(--brand-700);
            transform: translateY(-1px);
        }

        /* Panel espansibile del footer */
        .footer-expanded-panel {
            position: fixed;
            bottom: 50px;
            left: 0;
            right: 0;
            height: 200px;
            background: var(--color-surface);
            border-top: 2px solid var(--color-border);
            box-shadow:
                0 -4px 25px rgba(0, 0, 0, 0.15),
                0 -2px 15px rgba(0, 0, 0, 0.1),
                0 0 50px rgba(0, 0, 0, 0.08);
            transform: translateY(100%);
            transition: transform 0.3s ease;
            z-index: 99;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        .footer-expanded-panel.open {
            transform: translateY(0);
        }

        .expanded-panel-header {
            background: var(--color-background-light);
            padding: 1rem;
            border-bottom: 1px solid var(--color-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .expanded-panel-header h3 {
            color: var(--color-text-primary);
            margin: 0;
        }

        .close-expanded-panel {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--color-text-secondary);
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .close-expanded-panel:hover {
            background: var(--color-error);
            color: white;
        }

        .expanded-panel-content {
            padding: 1rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .info-card {
            background: var(--color-background-light);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--brand-600);
        }

        .info-card h4 {
            margin: 0 0 0.5rem 0;
            color: var(--color-text-primary);
            font-size: 0.9rem;
        }

        .info-card .value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--brand-600);
        }

        /* Login form */
        .login-section {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));
        }

        .login-form {
            max-width: 400px;
            width: 100%;
            margin: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .login-header {
            text-align: center;
            margin-bottom: 1rem;
            margin-top: 1rem;
        }

        .login-header .logo {
            width: 315px;
            height: 65px;
            margin-bottom: 0rem;
        }

        .login-header h1 {
            font-size: 2.5rem;
            color: var(--brand-900);
            margin: 0;
            font-weight: 700;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        /* Lista veicoli */
        .vehicles-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .vehicle-item {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 8px;
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 4px rgba(0, 0, 0, 0.06),
                0 0 20px rgba(0, 0, 0, 0.04);
            border: 2px solid transparent;
            border-left: 4px solid #2ecc71;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .vehicle-item:hover {
            background: #f8f9fa;
            transform: translateX(2px);
        }

        .vehicle-item.selected {
            background: #e8f4fd;
            border-left-color: #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
        }

        .vehicle-name {
            margin-bottom: 0.25rem;
            color: #2c3e50;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .vehicle-description {
            font-size: 0.8rem;
            color: #2c3e50;
            line-height: 1.4;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .left-column {
                width: 50px;
            }

            .selection-btn {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .sidebar {
                left: 50px;
                width: 280px;
            }

            .footer-info {
                gap: 1rem;
            }

            .info-item {
                font-size: 0.8rem;
            }

            .footer-expand-btn {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }
        }

        /* Loading e errori */
        .loading {
            text-align: center;
            padding: 0rem;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header identico alla pagina admin -->
    <div class="header">
        <h1>
            <img src="images/logo-ecotrac-bianco.png" alt="Logo" class="logo">
        </h1>
        <div class="user-info">
            <span id="username"></span>
            <button class="btn btn-danger" onclick="logout()" style="display: none;" id="logoutBtn">Esci</button>
        </div>
    </div>

    <!-- Login Form -->
    <div id="loginSection" class="login-section">
        <div class="login-form">
            <div class="login-header">
                <img src="images/logo-ecotrac-verde.png" alt="Logo" class="logo">
            </div>
            <h2 style="text-align: center; margin-bottom: 2rem; color: var(--color-text-secondary);">Accedi al sistema</h2>
            <div id="loginError" class="error" style="display: none;"></div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Nome utente:</label>
                    <input type="text" id="loginUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <button type="submit" class="btn" style="width: 100%;">Accedi</button>
            </form>
        </div>
    </div>

    <!-- Applicazione principale -->
    <div id="mainApp" style="display: none;">
        <!-- Contenuto principale -->
        <div class="main-content">
            <!-- Colonna sinistra - Barra di selezione -->
            <div class="left-column">
                <button type="button" class="selection-btn" onclick="toggleSidebar('vehicles')" title="Elenco Veicoli">
                    🚗
                </button>
                <button type="button" class="selection-btn" onclick="toggleSidebar('info')" title="Informazioni Sistema">
                    📊
                </button>
            </div>

            <!-- Sidebar Veicoli -->
            <div id="vehiclesSidebar" class="sidebar">
                <div class="sidebar-header">
                    <h3>Flotta Veicoli</h3>
                    <button class="close-sidebar" onclick="closeSidebar('vehicles')">&times;</button>
                </div>
                <div class="sidebar-content">
                    <div id="vehiclesContainer" class="loading">Caricamento veicoli...</div>
                </div>
            </div>

            <!-- Sidebar Informazioni -->
            <div id="infoSidebar" class="sidebar">
                <div class="sidebar-header">
                    <h3>Informazioni Sistema</h3>
                    <button class="close-sidebar" onclick="closeSidebar('info')">&times;</button>
                </div>
                <div class="sidebar-content">
                    <div class="info-grid">
                        <div class="info-card">
                            <h4>Veicoli Totali</h4>
                            <div class="value" id="totalVehicles">-</div>
                        </div>
                        <div class="info-card">
                            <h4>Veicoli Online</h4>
                            <div class="value" id="onlineVehicles">-</div>
                        </div>
                        <div class="info-card">
                            <h4>Ultimo Aggiornamento</h4>
                            <div class="value" id="lastUpdate">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Colonna destra - Mappa -->
            <div class="right-column">
                <div class="map-container">
                    <div id="map"></div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-info">
                <div class="info-item">
                    <span class="icon">🚗</span>
                    <span>Veicoli Online:</span>
                    <span class="value" id="footerOnlineCount">-</span>
                </div>
                <div class="info-item">
                    <span class="icon">📡</span>
                    <span>Ultimo Aggiornamento:</span>
                    <span class="value" id="footerLastUpdate">-</span>
                </div>
                <div class="info-item">
                    <span class="icon">⚡</span>
                    <span>Sistema:</span>
                    <span class="value">Online</span>
                </div>
            </div>
            <button class="footer-expand-btn" onclick="toggleFooterPanel()">
                <span>📈</span>
                <span>Statistiche Avanzate</span>
            </button>
        </div>

        <!-- Panel espandibile del footer -->
        <div id="footerExpandedPanel" class="footer-expanded-panel">
            <div class="expanded-panel-header">
                <h3>Statistiche Avanzate Sistema</h3>
                <button class="close-expanded-panel" onclick="closeFooterPanel()">&times;</button>
            </div>
            <div class="expanded-panel-content">
                <div class="info-grid">
                    <div class="info-card">
                        <h4>Veicoli Totali</h4>
                        <div class="value" id="expandedTotalVehicles">-</div>
                    </div>
                    <div class="info-card">
                        <h4>Veicoli Online</h4>
                        <div class="value" id="expandedOnlineVehicles">-</div>
                    </div>
                    <div class="info-card">
                        <h4>Veicoli Offline</h4>
                        <div class="value" id="expandedOfflineVehicles">-</div>
                    </div>
                    <div class="info-card">
                        <h4>Ultimo Aggiornamento</h4>
                        <div class="value" id="expandedLastUpdate">-</div>
                    </div>
                    <div class="info-card">
                        <h4>Connessioni Attive</h4>
                        <div class="value">24/7</div>
                    </div>
                    <div class="info-card">
                        <h4>Uptime Sistema</h4>
                        <div class="value">99.9%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="toast.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let vehicleMarkers = {};
        let token = localStorage.getItem('token');
        let activeSidebar = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            if (token) {
                verifyToken();
            } else {
                showLogin();
            }
        });

        // Show login form
        function showLogin() {
            document.getElementById('loginSection').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('logoutBtn').style.display = 'none';
        }

        // Show main app
        function showMainApp() {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';
            document.getElementById('logoutBtn').style.display = 'block';
            initializeMap();
            loadVehicles();
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    document.getElementById('username').textContent = data.data.user.username;
                    showMainApp();
                } else {
                    showToastError(data.message);
                }
            } catch (error) {
                showToastError('Accesso fallito. Riprova.');
            }
        });

        // Verify token
        async function verifyToken() {
            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('username').textContent = data.data.user.username;
                    showMainApp();
                } else {
                    localStorage.removeItem('token');
                    token = null;
                    showLogin();
                }
            } catch (error) {
                localStorage.removeItem('token');
                token = null;
                showLogin();
            }
        }

        // Logout
        function logout() {
            localStorage.removeItem('token');
            token = null;
            showLogin();
        }

        // Initialize map
        function initializeMap() {
            if (map) return;

            map = L.map('map', {
                zoomControl: true,
                attributionControl: true
            }).setView([54.6872, 25.2797], 7); // Lithuania center

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Adjust map size when panels open/close
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
        }

        // Toggle sidebar
        function toggleSidebar(sidebarType) {
            const vehiclesSidebar = document.getElementById('vehiclesSidebar');
            const infoSidebar = document.getElementById('infoSidebar');
            const buttons = document.querySelectorAll('.selection-btn');

            // Close all sidebars
            vehiclesSidebar.classList.remove('open');
            infoSidebar.classList.remove('open');
            buttons.forEach(btn => btn.classList.remove('active'));

            // If clicking the same sidebar, just close it
            if (activeSidebar === sidebarType) {
                activeSidebar = null;
                return;
            }

            // Open the requested sidebar
            activeSidebar = sidebarType;
            if (sidebarType === 'vehicles') {
                vehiclesSidebar.classList.add('open');
                buttons[0].classList.add('active');
            } else if (sidebarType === 'info') {
                infoSidebar.classList.add('open');
                buttons[1].classList.add('active');
            }

            // Adjust map size
            setTimeout(() => {
                if (map) map.invalidateSize();
            }, 300);
        }

        // Close sidebar
        function closeSidebar(sidebarType) {
            if (sidebarType === 'vehicles') {
                document.getElementById('vehiclesSidebar').classList.remove('open');
            } else if (sidebarType === 'info') {
                document.getElementById('infoSidebar').classList.remove('open');
            }

            const buttons = document.querySelectorAll('.selection-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            activeSidebar = null;

            // Adjust map size
            setTimeout(() => {
                if (map) map.invalidateSize();
            }, 300);
        }

        // Toggle footer panel
        function toggleFooterPanel() {
            const panel = document.getElementById('footerExpandedPanel');
            panel.classList.toggle('open');
        }

        // Close footer panel
        function closeFooterPanel() {
            document.getElementById('footerExpandedPanel').classList.remove('open');
        }

        // Load vehicles
        async function loadVehicles() {
            try {
                const response = await fetch('/api/gps/vehicles', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    displayVehicles(data.data);
                    updateMap(data.data);
                    updateInfoDisplays(data.data);
                } else {
                    showToastError(data.message);
                }
            } catch (error) {
                showToastError('Impossibile caricare i veicoli.');
            }
        }

        // Display vehicles in sidebar
        function displayVehicles(vehicles) {
            const container = document.getElementById('vehiclesContainer');

            if (vehicles.length === 0) {
                container.innerHTML = '<p>Nessun veicolo trovato.</p>';
                return;
            }

            // Ordina i veicoli per nome in ordine crescente
            const sortedVehicles = vehicles.sort((a, b) => {
                const nameA = (a.name || `Vehicle ${a.imei}`).toLowerCase();
                const nameB = (b.name || `Vehicle ${b.imei}`).toLowerCase();
                return nameA.localeCompare(nameB);
            });

            container.innerHTML = '<div class="vehicles-list"></div>';
            const listContainer = container.querySelector('.vehicles-list');

            sortedVehicles.forEach(vehicle => {
                const vehicleElement = document.createElement('div');
                vehicleElement.className = 'vehicle-item';
                vehicleElement.onclick = () => selectVehicle(vehicle.id, vehicle);

                vehicleElement.innerHTML = `
                    <div class="vehicle-name">${vehicle.name || `Vehicle ${vehicle.imei}`}</div>
                    <div class="vehicle-description">${vehicle.description || 'Nessuna descrizione disponibile'}</div>
                `;

                listContainer.appendChild(vehicleElement);
            });
        }

        // Select vehicle
        function selectVehicle(vehicleId, vehicle) {
            // Rimuovi la selezione precedente
            document.querySelectorAll('.vehicle-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Seleziona il veicolo cliccato
            event.target.closest('.vehicle-item').classList.add('selected');

            // Centra la mappa sulla posizione del veicolo selezionato
            if (vehicle.latitude && vehicle.longitude) {
                const lat = parseFloat(vehicle.latitude);
                const lng = parseFloat(vehicle.longitude);
                map.setView([lat, lng], 16);

                // Evidenzia il marker del veicolo selezionato
                Object.values(vehicleMarkers).forEach(marker => {
                    marker.setOpacity(0.6);
                });

                if (vehicleMarkers[vehicle.imei]) {
                    vehicleMarkers[vehicle.imei].setOpacity(1.0);
                    vehicleMarkers[vehicle.imei].openPopup();
                }
            }

            showToastSuccess(`Veicolo selezionato: ${vehicle.name || vehicle.imei}`);
        }

        // Update map with vehicle positions
        function updateMap(vehicles) {
            // Clear existing markers
            Object.values(vehicleMarkers).forEach(marker => {
                map.removeLayer(marker);
            });
            vehicleMarkers = {};

            const bounds = [];

            vehicles.forEach(vehicle => {
                if (vehicle.latitude && vehicle.longitude) {
                    const lat = parseFloat(vehicle.latitude);
                    const lng = parseFloat(vehicle.longitude);

                    const isOnline = vehicle.device_timestamp &&
                        (new Date() - new Date(vehicle.device_timestamp)) < 300000;

                    const icon = L.divIcon({
                        html: `<div style="background: ${isOnline ? 'var(--color-success)' : 'var(--color-error)'};
                                     width: 12px; height: 12px; border-radius: 50%;
                                     border: 2px solid white; box-shadow: var(--shadow-md);"></div>`,
                        iconSize: [16, 16],
                        className: 'vehicle-marker'
                    });

                    const marker = L.marker([lat, lng], { icon })
                        .bindPopup(`
                            <strong>${vehicle.name || `Vehicle ${vehicle.imei}`}</strong><br>
                            IMEI: ${vehicle.imei}<br>
                            Speed: ${vehicle.speed || 0} km/h<br>
                            Direction: ${vehicle.direction || 0}°<br>
                            Ignition: ${vehicle.ignition_status ? 'ON' : 'OFF'}<br>
                            Last Update: ${vehicle.device_timestamp ?
                                new Date(vehicle.device_timestamp).toLocaleString() : 'Never'}
                        `);

                    marker.addTo(map);
                    vehicleMarkers[vehicle.imei] = marker;
                    bounds.push([lat, lng]);
                }
            });

            // Fit map to show all vehicles
            if (bounds.length > 0) {
                map.fitBounds(bounds, { padding: [20, 20] });
            }
        }

        // Update info displays
        function updateInfoDisplays(vehicles) {
            const now = new Date();
            const onlineVehicles = vehicles.filter(v =>
                v.device_timestamp && (now - new Date(v.device_timestamp)) < 300000
            );

            // Update footer info
            document.getElementById('footerOnlineCount').textContent = onlineVehicles.length;
            document.getElementById('footerLastUpdate').textContent = vehicles.length > 0 && vehicles[0].device_timestamp
                ? new Date(vehicles[0].device_timestamp).toLocaleTimeString()
                : '-';

            // Update sidebar info
            document.getElementById('totalVehicles').textContent = vehicles.length;
            document.getElementById('onlineVehicles').textContent = onlineVehicles.length;
            document.getElementById('lastUpdate').textContent = vehicles.length > 0 && vehicles[0].device_timestamp
                ? new Date(vehicles[0].device_timestamp).toLocaleTimeString()
                : '-';

            // Update expanded panel info
            document.getElementById('expandedTotalVehicles').textContent = vehicles.length;
            document.getElementById('expandedOnlineVehicles').textContent = onlineVehicles.length;
            document.getElementById('expandedOfflineVehicles').textContent = vehicles.length - onlineVehicles.length;
            document.getElementById('expandedLastUpdate').textContent = vehicles.length > 0 && vehicles[0].device_timestamp
                ? new Date(vehicles[0].device_timestamp).toLocaleString()
                : '-';
        }

        // Auto-refresh vehicles every 30 seconds
        setInterval(() => {
            if (token && document.getElementById('mainApp').style.display !== 'none') {
                loadVehicles();
            }
        }, 30000);

        // Handle window resize
        window.addEventListener('resize', function() {
            if (map) {
                setTimeout(() => {
                    map.invalidateSize();
                }, 100);
            }
        });
    </script>
</body>
</html>
