const net = require('net');
const { parseIM<PERSON>, ProtocolParser } = require('complete-teltonika-parser');
const logger = require('../utils/logger');
const { saveGpsData, getVehicleByIMEI } = require('../services/gpsService');

class TeltonikaServer {
  constructor(port = 8090) {
    this.port = port;
    this.server = null;
    this.clients = new Map(); // Store client connections with their IMEI
  }

  start() {
    this.server = net.createServer((socket) => {
      this.handleConnection(socket);
    });

    this.server.listen(this.port, () => {
      logger.info(`Teltonika TCP server listening on port ${this.port}`);
    });

    this.server.on('error', (error) => {
      logger.error('TCP server error:', error);
    });

    return this.server;
  }

  handleConnection(socket) {
    const clientId = `${socket.remoteAddress}:${socket.remotePort}`;
    logger.info(`New connection from ${clientId}`);

    let clientData = {
      socket: socket,
      imei: null,
      authenticated: false,
      buffer: Buffer.alloc(0)
    };

    this.clients.set(clientId, clientData);

    socket.on('data', (data) => {
      this.handleData(clientId, data);
    });

    socket.on('close', () => {
      logger.info(`Connection closed: ${clientId}`);
      this.clients.delete(clientId);
    });

    socket.on('error', (error) => {
      logger.error(`Socket error for ${clientId}:`, error);
      this.clients.delete(clientId);
    });

    // Set timeout for authentication
    setTimeout(() => {
      if (!clientData.authenticated) {
        logger.warn(`Authentication timeout for ${clientId}`);
        socket.destroy();
      }
    }, 30000); // 30 seconds timeout
  }

  handleData(clientId, data) {
    try {
      const client = this.clients.get(clientId);
      if (!client) return;

      // Append new data to buffer
      client.buffer = Buffer.concat([client.buffer, data]);

      // Process packets from buffer
      this.processBuffer(clientId);

    } catch (error) {
      logger.error(`Error handling data from ${clientId}:`, error);
    }
  }

  processBuffer(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const hexData = client.buffer.toString('hex').toUpperCase();

    if (!client.authenticated) {
      // Check if we have enough data for IMEI (17 bytes = 34 hex characters)
      if (hexData.length >= 34) {
        this.handleIMEI(clientId, hexData.substring(0, 34));
        // Remove processed IMEI data from buffer
        client.buffer = client.buffer.slice(17);
      }
    } else {
      // Process GPS data packets
      this.processGpsPackets(clientId);
    }
  }

  async handleIMEI(clientId, imeiHex) {
    try {
      const client = this.clients.get(clientId);
      if (!client) return;

      const imei = parseIMEI(imeiHex);
      logger.info(`IMEI received from ${clientId}: ${imei}`);

      // Check if vehicle exists in database
      const vehicle = await getVehicleByIMEI(imei);

      if (!vehicle) {
        logger.warn(`Rejecting connection from unregistered IMEI ${imei} from ${clientId}`);
        const client = this.clients.get(clientId);
        if (client) {
          client.socket.destroy();
        }
        return;
      }

      client.imei = imei;
      client.authenticated = true;

      // Send acknowledgment (0x01)
      const ack = Buffer.from([0x01]);
      client.socket.write(ack);

      logger.info(`IMEI ${imei} authenticated successfully for registered vehicle: ${vehicle.name}`);

    } catch (error) {
      logger.error(`Error handling IMEI from ${clientId}:`, error);
      const client = this.clients.get(clientId);
      if (client) {
        client.socket.destroy();
      }
    }
  }

  processGpsPackets(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const hexData = client.buffer.toString('hex').toUpperCase();
    
    // Minimum packet size check (need at least preamble + data length)
    if (hexData.length < 16) return; // 8 bytes = 16 hex chars

    try {
      // Parse the packet
      const parser = new ProtocolParser(hexData);
      
      if (parser.CodecType === 'data sending' && parser.Content) {
        this.handleGpsData(clientId, parser);
        
        // Send acknowledgment with number of records received
        const recordCount = parser.Content.AVL_Datas ? parser.Content.AVL_Datas.length : 0;
        const ack = Buffer.from([0x00, 0x00, 0x00, recordCount]);
        client.socket.write(ack);
        
        logger.info(`Processed ${recordCount} GPS records from IMEI ${client.imei}`);
      }

      // Calculate packet length and remove processed data from buffer
      const packetLength = 8 + parser.Data_Length + 4; // preamble + data + CRC
      const bytesToRemove = packetLength;
      
      if (client.buffer.length >= bytesToRemove) {
        client.buffer = client.buffer.slice(bytesToRemove);
      } else {
        client.buffer = Buffer.alloc(0);
      }

      // Process remaining data if any
      if (client.buffer.length > 0) {
        this.processGpsPackets(clientId);
      }

    } catch (error) {
      logger.error(`Error processing GPS packet from ${clientId}:`, error);
      // Clear buffer on error to prevent infinite loop
      client.buffer = Buffer.alloc(0);
    }
  }

  async handleGpsData(clientId, parser) {
    const client = this.clients.get(clientId);
    if (!client || !parser.Content || !parser.Content.AVL_Datas) return;

    const imei = client.imei;

    // Double-check that the IMEI is from a registered vehicle
    const vehicle = await getVehicleByIMEI(imei);
    if (!vehicle) {
      logger.warn(`Rejecting GPS data from unregistered IMEI ${imei} from ${clientId}`);
      const client = this.clients.get(clientId);
      if (client) {
        client.socket.destroy();
      }
      return;
    }

    const avlData = parser.Content.AVL_Datas;

    for (const data of avlData) {
      try {
        const gpsRecord = {
          imei: imei,
          latitude: data.GPSelement.Latitude,
          longitude: data.GPSelement.Longitude,
          device_timestamp: data.Timestamp,
          speed: data.GPSelement.Speed,
          direction: data.GPSelement.Angle,
          altitude: data.GPSelement.Altitude,
          priority: data.Priority,
          satellites: data.GPSelement.Satellites,
          variable_data: data.IOelement ? {
            event_id: data.IOelement.EventID,
            elements: data.IOelement.Elements
          } : null
        };

        // Extract common IO elements
        if (data.IOelement && data.IOelement.Elements) {
          const elements = data.IOelement.Elements;

          // Ignition status (IO ID 239)
          if (elements[239] !== undefined) {
            gpsRecord.ignition_status = elements[239] === 1;
          }

          // Battery voltage from IO 67 (specific ID)
          if (elements[67] !== undefined) {
            gpsRecord.battery_voltage = parseFloat(elements[67]) * 0.001;
          }

          // External voltage from IO 66 (specific ID)
          if (elements[66] !== undefined) {
            gpsRecord.external_voltage = parseFloat(elements[66]) * 0.001;
          }

          // Total odometer from IO 16 (specific ID)
          if (elements[16] !== undefined) {
            gpsRecord.total_odometer = parseFloat(elements[16]) * 0.001;
          }
        }

        await saveGpsData(gpsRecord);

      } catch (error) {
        logger.error(`Error saving GPS data for IMEI ${imei}:`, error);
      }
    }
  }

  stop() {
    if (this.server) {
      this.server.close(() => {
        logger.info('TCP server stopped');
      });
      
      // Close all client connections
      this.clients.forEach((client, clientId) => {
        client.socket.destroy();
      });
      this.clients.clear();
    }
  }
}

module.exports = TeltonikaServer;
