const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const { authenticateToken } = require('../middleware/auth');
const { validateUserInput, validatePassword, validateVehicleInput } = require('../middleware/validation');
const { getUserById, getUserByUsername, getAllUsers, createUser, updateUser, deleteUser, updateUserPassword } = require('../services/userService');
const { getUserVehicles, createVehicleForUser, updateVehicle, deleteVehicle, getVehicleById, getVehicleBySimNumber } = require('../services/vehicleService');
const { getLatestPosition, getGpsData } = require('../services/gpsService');
const { convertDateRangeToUTC, getUserTimezone } = require('../utils/timezone');

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (req.user.account_type !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Admin privileges required.'
    });
  }
  next();
};

/**
 * GET /api/admin/users
 * Get all users (admin only)
 */
router.get('/users', 
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const users = await getAllUsers();
      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      console.error('Error getting users:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * POST /api/admin/users
 * Create a new user (admin only)
 */
router.post('/users',
  authenticateToken,
  requireAdmin,
  validateUserInput,
  async (req, res) => {
    try {
      const { username, password, account_type } = req.body;

      const userData = { username, password, account_type };

      const newUser = await createUser(userData);

      res.status(201).json({
        success: true,
        data: newUser,
        message: 'User created successfully'
      });
    } catch (error) {
      console.error('Error creating user:', error);

      if (error.message === 'Username already exists') {
        return res.status(409).json({
          success: false,
          message: 'Username already exists'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * PUT /api/admin/users/:id
 * Update a user (admin only)
 */
router.put('/users/:id',
  authenticateToken,
  requireAdmin,
  validateUserInput,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const { username, account_type } = req.body;
      
      // Check if user exists
      const existingUser = await getUserById(userId);
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }
      
      // If username is being changed, check if it already exists
      if (username && username !== existingUser.username) {
        const userWithUsername = await getUserByUsername(username);
        if (userWithUsername) {
          return res.status(400).json({
            success: false,
            message: 'Username already exists'
          });
        }
      }
      
      const updatedUser = await updateUser(userId, { username, account_type });
      
      res.json({
        success: true,
        data: updatedUser,
        message: 'User updated successfully'
      });
    } catch (error) {
      console.error('Error updating user:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * PUT /api/admin/users/:id/password
 * Change a user's password (admin only)
 */
router.put('/users/:id/password',
  authenticateToken,
  requireAdmin,
  validatePassword,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const { password } = req.body;
      
      // Check if user exists
      const existingUser = await getUserById(userId);
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }
      
      // Hash new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);
      
      await updateUserPassword(userId, hashedPassword);
      
      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error) {
      console.error('Error changing password:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * DELETE /api/admin/users/:id
 * Delete a user (admin only)
 */
router.delete('/users/:id',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);

      // Check if user exists
      const existingUser = await getUserById(userId);
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Prevent deleting the current user
      if (userId === req.user.id) {
        return res.status(400).json({
          success: false,
          message: 'You cannot delete your own account'
        });
      }

      // Check if user has associated vehicles
      const userVehicles = await getUserVehicles(userId);
      if (userVehicles && userVehicles.length > 0) {
        return res.status(409).json({
          success: false,
          message: `Cannot delete user: ${userVehicles.length} vehicle(s) still associated with this user. Please remove all vehicles first.`
        });
      }

      await deleteUser(userId);

      res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/admin/users/:userId/vehicles
 * Get all vehicles for a specific user (admin only)
 */
router.get('/users/:userId/vehicles',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);

      // Check if user exists
      const user = await getUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const vehicles = await getUserVehicles(userId);
      res.json({
        success: true,
        data: vehicles
      });
    } catch (error) {
      console.error('Error getting user vehicles:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * POST /api/admin/users/:userId/vehicles
 * Create a new vehicle for a specific user (admin only)
 */
router.post('/users/:userId/vehicles',
  authenticateToken,
  requireAdmin,
  validateVehicleInput,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const { imei, sim_number, name, description } = req.body;

      // Check if user exists
      const user = await getUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const vehicleData = { imei, sim_number, name, description };

      const newVehicle = await createVehicleForUser(vehicleData, userId);

      res.status(201).json({
        success: true,
        data: newVehicle,
        message: 'Vehicle created and assigned successfully'
      });
    } catch (error) {
      console.error('Error creating vehicle for user:', error);

      if (error.message === 'Vehicle with this IMEI already exists') {
        return res.status(409).json({
          success: false,
          message: 'Vehicle with this IMEI already exists'
        });
      }

      if (error.message && error.message.includes('duplicate key value violates unique constraint')) {
        return res.status(409).json({
          success: false,
          message: 'SIM number already exists'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * PUT /api/admin/users/:userId/vehicles/:vehicleId
 * Update a vehicle for a specific user (admin only)
 */
router.put('/users/:userId/vehicles/:vehicleId',
  authenticateToken,
  requireAdmin,
  validateVehicleInput,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const vehicleId = parseInt(req.params.vehicleId);
      const { imei, sim_number, name, description } = req.body;

      // Check if user exists
      const user = await getUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check if vehicle exists
      const existingVehicle = await getVehicleById(vehicleId);
      if (!existingVehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehicle not found'
        });
      }

      // If IMEI is being changed, check if it already exists
      if (imei && imei !== existingVehicle.imei) {
        const vehicleWithImei = await getVehicleByImei(imei);
        if (vehicleWithImei) {
          return res.status(400).json({
            success: false,
            message: 'IMEI already exists'
          });
        }
      }

      // If SIM number is being changed, check if it already exists
      if (sim_number && sim_number !== existingVehicle.sim_number) {
        const vehicleWithSimNumber = await getVehicleBySimNumber(sim_number);
        if (vehicleWithSimNumber) {
          return res.status(400).json({
            success: false,
            message: 'SIM number already exists'
          });
        }
      }

      const updatedVehicle = await updateVehicle(vehicleId, { imei, sim_number, name, description });

      res.json({
        success: true,
        data: updatedVehicle,
        message: 'Vehicle updated successfully'
      });
    } catch (error) {
      console.error('Error updating vehicle:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/admin/users/:userId/vehicles/:vehicleId
 * Get a specific vehicle for a specific user (admin only)
 */
router.get('/users/:userId/vehicles/:vehicleId',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const vehicleId = parseInt(req.params.vehicleId);

      // Check if user exists
      const user = await getUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Get vehicle by ID
      const vehicle = await getVehicleById(vehicleId);
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehicle not found'
        });
      }

      res.json({
        success: true,
        data: vehicle
      });
    } catch (error) {
      console.error('Error getting vehicle:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * DELETE /api/admin/users/:userId/vehicles/:vehicleId
 * Delete a vehicle assigned to a user (admin only)
 */
router.delete('/users/:userId/vehicles/:vehicleId',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const vehicleId = parseInt(req.params.vehicleId);

      // Check if user exists
      const user = await getUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check if vehicle exists
      const existingVehicle = await getVehicleById(vehicleId);
      if (!existingVehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehicle not found'
        });
      }

      await deleteVehicle(vehicleId);

      res.json({
        success: true,
        message: 'Vehicle deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/admin/vehicles/:vehicleId
 * Get vehicle information by ID (admin only)
 */
router.get('/vehicles/:vehicleId',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const vehicleId = parseInt(req.params.vehicleId);

      // Get vehicle by ID
      const vehicle = await getVehicleById(vehicleId);
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehicle not found'
        });
      }

      res.json({
        success: true,
        data: vehicle
      });
    } catch (error) {
      console.error('Error getting vehicle:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/admin/vehicles/:vehicleId/latest-position
 * Get latest position for a vehicle (admin only)
 */
router.get('/vehicles/:vehicleId/latest-position',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const vehicleId = parseInt(req.params.vehicleId);

      // Check if vehicle exists
      const vehicle = await getVehicleById(vehicleId);
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehicle not found'
        });
      }

      const latestPosition = await getLatestPosition(vehicle.imei);

      res.json({
        success: true,
        data: latestPosition
      });
    } catch (error) {
      console.error('Error getting latest vehicle position:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/admin/vehicles/:vehicleId/messages
 * Get GPS messages for a vehicle with filtering (admin only)
 */
router.get('/vehicles/:vehicleId/messages',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const vehicleId = parseInt(req.params.vehicleId);

      // Check if vehicle exists
      const vehicle = await getVehicleById(vehicleId);
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehicle not found'
        });
      }

      // Get user's timezone
      const userTimezone = getUserTimezone(req);

      // Convert date range from user's timezone to UTC for database queries
      let startDateUTC = null;
      let endDateUTC = null;

      if (req.query.startDate || req.query.endDate) {
        const dateRange = convertDateRangeToUTC(req.query.startDate, req.query.endDate, userTimezone);
        startDateUTC = dateRange.startUTC;
        endDateUTC = dateRange.endUTC;
      }

      const options = {
        limit: parseInt(req.query.limit) || 100,
        offset: parseInt(req.query.offset) || 0,
        startDate: startDateUTC,
        endDate: endDateUTC,
        orderBy: req.query.orderBy || 'device_timestamp',
        order: req.query.order || 'DESC'
      };

      const messages = await getGpsData(vehicle.imei, options);

      res.json({
        success: true,
        data: messages.data,
        pagination: messages.pagination
      });
    } catch (error) {
      console.error('Error getting vehicle messages:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;
